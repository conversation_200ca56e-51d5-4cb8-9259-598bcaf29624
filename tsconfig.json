{"compilerOptions": {"strict": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "baseUrl": "./src", "paths": {"@/*": ["*"], "@/components/*": ["components/*"], "@/services/*": ["services/*"], "@/types/*": ["types/*"], "@/utils/*": ["utils/*"], "@/stores/*": ["stores/*"], "@/screens/*": ["screens/*"], "@/theme/*": ["theme/*"], "@/theme": ["theme/index"]}, "allowJs": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "jsx": "react-native", "lib": ["dom", "esnext"], "moduleResolution": "node", "noEmit": true, "skipLibCheck": true, "resolveJsonModule": true}, "include": ["src/**/*", "App.js", "app.json"], "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"], "extends": "expo/tsconfig.base"}