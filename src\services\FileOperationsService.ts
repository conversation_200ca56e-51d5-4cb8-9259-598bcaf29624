import * as Sharing from 'expo-sharing';
import * as FileSystem from 'expo-file-system';
import { Alert } from 'react-native';
import { FileItem } from '../types';

export class FileOperationsService {
  private static instance: FileOperationsService;

  public static getInstance(): FileOperationsService {
    if (!FileOperationsService.instance) {
      FileOperationsService.instance = new FileOperationsService();
    }
    return FileOperationsService.instance;
  }

  private constructor() {}

  /**
   * Share a file using the system share dialog
   */
  async shareFile(file: FileItem): Promise<void> {
    try {
      const isAvailable = await Sharing.isAvailableAsync();
      if (!isAvailable) {
        Alert.alert('Error', 'Sharing is not available on this device');
        return;
      }

      await Sharing.shareAsync(file.path, {
        mimeType: file.mimeType || 'application/octet-stream',
        dialogTitle: `Share ${file.name}`,
      });
    } catch (error) {
      console.error('Failed to share file:', error);
      Alert.alert('Error', 'Failed to share file');
    }
  }

  /**
   * Share multiple files
   */
  async shareMultipleFiles(files: FileItem[]): Promise<void> {
    try {
      if (files.length === 0) {
        Alert.alert('Error', 'No files selected');
        return;
      }

      if (files.length === 1) {
        await this.shareFile(files[0]);
        return;
      }

      // For multiple files, we'll need to create a temporary directory
      // and copy files there, then share the directory
      const tempDir = `${FileSystem.cacheDirectory}shared_files_${Date.now()}/`;
      await FileSystem.makeDirectoryAsync(tempDir, { intermediates: true });

      try {
        // Copy all files to temp directory
        for (const file of files) {
          const fileName = file.name;
          const destPath = `${tempDir}${fileName}`;
          await FileSystem.copyAsync({
            from: file.path,
            to: destPath,
          });
        }

        // Share the directory
        const isAvailable = await Sharing.isAvailableAsync();
        if (!isAvailable) {
          Alert.alert('Error', 'Sharing is not available on this device');
          return;
        }

        await Sharing.shareAsync(tempDir, {
          dialogTitle: `Share ${files.length} files`,
        });
      } finally {
        // Clean up temp directory
        try {
          await FileSystem.deleteAsync(tempDir);
        } catch (cleanupError) {
          console.warn('Failed to clean up temp directory:', cleanupError);
        }
      }
    } catch (error) {
      console.error('Failed to share multiple files:', error);
      Alert.alert('Error', 'Failed to share files');
    }
  }

  /**
   * Copy files to a new location
   */
  async copyFiles(files: FileItem[], destinationPath: string): Promise<void> {
    try {
      const results = await Promise.allSettled(
        files.map(async (file) => {
          const fileName = file.name;
          const destPath = `${destinationPath}/${fileName}`;
          
          // Check if destination already exists
          const destInfo = await FileSystem.getInfoAsync(destPath);
          if (destInfo.exists) {
            throw new Error(`File ${fileName} already exists at destination`);
          }

          await FileSystem.copyAsync({
            from: file.path,
            to: destPath,
          });
        })
      );

      const failures = results.filter(result => result.status === 'rejected');
      if (failures.length > 0) {
        const failedCount = failures.length;
        const successCount = files.length - failedCount;
        Alert.alert(
          'Copy Results',
          `${successCount} files copied successfully. ${failedCount} files failed to copy.`
        );
      } else {
        Alert.alert('Success', `${files.length} files copied successfully`);
      }
    } catch (error) {
      console.error('Failed to copy files:', error);
      Alert.alert('Error', 'Failed to copy files');
    }
  }

  /**
   * Move files to a new location
   */
  async moveFiles(files: FileItem[], destinationPath: string): Promise<void> {
    try {
      const results = await Promise.allSettled(
        files.map(async (file) => {
          const fileName = file.name;
          const destPath = `${destinationPath}/${fileName}`;
          
          // Check if destination already exists
          const destInfo = await FileSystem.getInfoAsync(destPath);
          if (destInfo.exists) {
            throw new Error(`File ${fileName} already exists at destination`);
          }

          await FileSystem.moveAsync({
            from: file.path,
            to: destPath,
          });
        })
      );

      const failures = results.filter(result => result.status === 'rejected');
      if (failures.length > 0) {
        const failedCount = failures.length;
        const successCount = files.length - failedCount;
        Alert.alert(
          'Move Results',
          `${successCount} files moved successfully. ${failedCount} files failed to move.`
        );
      } else {
        Alert.alert('Success', `${files.length} files moved successfully`);
      }
    } catch (error) {
      console.error('Failed to move files:', error);
      Alert.alert('Error', 'Failed to move files');
    }
  }

  /**
   * Delete multiple files
   */
  async deleteFiles(files: FileItem[]): Promise<void> {
    try {
      const results = await Promise.allSettled(
        files.map(async (file) => {
          await FileSystem.deleteAsync(file.path);
        })
      );

      const failures = results.filter(result => result.status === 'rejected');
      if (failures.length > 0) {
        const failedCount = failures.length;
        const successCount = files.length - failedCount;
        Alert.alert(
          'Delete Results',
          `${successCount} files deleted successfully. ${failedCount} files failed to delete.`
        );
      } else {
        Alert.alert('Success', `${files.length} files deleted successfully`);
      }
    } catch (error) {
      console.error('Failed to delete files:', error);
      Alert.alert('Error', 'Failed to delete files');
    }
  }

  /**
   * Get file information including size and type
   */
  async getFileDetails(file: FileItem): Promise<{
    size: string;
    type: string;
    lastModified: string;
    permissions: string;
  }> {
    try {
      await FileSystem.getInfoAsync(file.path);

      return {
        size: this.formatFileSize(file.size),
        type: file.mimeType || 'Unknown',
        lastModified: file.modifiedDate.toLocaleString(),
        permissions: this.formatPermissions(file.permissions),
      };
    } catch (error) {
      console.error('Failed to get file details:', error);
      throw error;
    }
  }

  /**
   * Create a new folder
   */
  async createFolder(parentPath: string, folderName: string): Promise<void> {
    try {
      const folderPath = `${parentPath}/${folderName}`;
      const info = await FileSystem.getInfoAsync(folderPath);
      
      if (info.exists) {
        Alert.alert('Error', 'A folder with this name already exists');
        return;
      }

      await FileSystem.makeDirectoryAsync(folderPath, { intermediates: true });
      Alert.alert('Success', `Folder "${folderName}" created successfully`);
    } catch (error) {
      console.error('Failed to create folder:', error);
      Alert.alert('Error', 'Failed to create folder');
    }
  }

  /**
   * Rename a file or folder
   */
  async renameItem(file: FileItem, newName: string): Promise<void> {
    try {
      const parentPath = file.path.substring(0, file.path.lastIndexOf('/'));
      const newPath = `${parentPath}/${newName}`;
      
      const info = await FileSystem.getInfoAsync(newPath);
      if (info.exists) {
        Alert.alert('Error', 'An item with this name already exists');
        return;
      }

      await FileSystem.moveAsync({
        from: file.path,
        to: newPath,
      });

      Alert.alert('Success', `Item renamed to "${newName}"`);
    } catch (error) {
      console.error('Failed to rename item:', error);
      Alert.alert('Error', 'Failed to rename item');
    }
  }

  // Helper methods
  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  private formatPermissions(permissions: any): string {
    const perms = [];
    if (permissions.readable) perms.push('Read');
    if (permissions.writable) perms.push('Write');
    if (permissions.executable) perms.push('Execute');
    return perms.join(', ') || 'None';
  }
}
