import React, { useState } from 'react';
import {
  TextInput,
  View,
  StyleSheet,
  ViewStyle,
  TextStyle,
  TextInputProps,
} from 'react-native';
import { useTheme } from '../theme';
import { Text } from './Text';

interface InputProps extends TextInputProps {
  label?: string;
  error?: string;
  containerStyle?: ViewStyle;
  inputStyle?: TextStyle;
  variant?: 'default' | 'outlined' | 'filled';
}

export const Input: React.FC<InputProps> = ({
  label,
  error,
  containerStyle,
  inputStyle,
  variant = 'outlined',
  ...props
}) => {
  const { theme } = useTheme();
  const [isFocused, setIsFocused] = useState(false);

  const getInputContainerStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      borderRadius: theme.radii.md,
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
    };

    const variantStyles = {
      default: {
        borderBottomWidth: 1,
        borderBottomColor: error
          ? theme.colors.error
          : isFocused
          ? theme.colors.primary
          : theme.colors.border,
      },
      outlined: {
        borderWidth: 1,
        borderColor: error
          ? theme.colors.error
          : isFocused
          ? theme.colors.primary
          : theme.colors.border,
        backgroundColor: theme.colors.background,
      },
      filled: {
        backgroundColor: theme.colors.surface,
        borderWidth: 1,
        borderColor: error
          ? theme.colors.error
          : isFocused
          ? theme.colors.primary
          : 'transparent',
      },
    };

    return {
      ...baseStyle,
      ...variantStyles[variant],
    };
  };

  const getInputStyle = (): TextStyle => ({
    fontSize: theme.typography.body.fontSize,
    color: theme.colors.text,
    fontWeight: theme.typography.body.fontWeight,
    lineHeight: theme.typography.body.lineHeight,
  });

  return (
    <View style={[styles.container, containerStyle]}>
      {label && (
        <Text
          variant="caption"
          color="textSecondary"
          weight="medium"
          style={styles.label}>
          {label}
        </Text>
      )}
      <View style={getInputContainerStyle()}>
        <TextInput
          style={[getInputStyle(), inputStyle]}
          placeholderTextColor={theme.colors.textSecondary}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          {...props}
        />
      </View>
      {error && (
        <Text variant="caption" color="error" style={styles.error}>
          {error}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    marginBottom: 4,
  },
  error: {
    marginTop: 4,
  },
});