/**
 * Animation Manager - Coordinates animations to prevent conflicts
 * Inspired by shadcn/ui's smooth, coordinated animations
 */

import { SharedValue, runOnJS, cancelAnimation } from 'react-native-reanimated';
import { PERFORMANCE_CONFIG } from './AnimationConfig';

interface AnimationInstance {
  id: string;
  sharedValue: SharedValue<number>;
  priority: 'low' | 'medium' | 'high';
  type: 'spring' | 'timing' | 'gesture';
  startTime: number;
}

class AnimationManager {
  private static instance: AnimationManager;
  private activeAnimations: Map<string, AnimationInstance> = new Map();
  private animationQueue: AnimationInstance[] = [];
  private isProcessingQueue = false;

  static getInstance(): AnimationManager {
    if (!AnimationManager.instance) {
      AnimationManager.instance = new AnimationManager();
    }
    return AnimationManager.instance;
  }

  /**
   * Register an animation to be managed
   */
  registerAnimation(
    id: string,
    sharedValue: SharedValue<number>,
    priority: 'low' | 'medium' | 'high' = 'medium',
    type: 'spring' | 'timing' | 'gesture' = 'spring'
  ): void {
    const animation: AnimationInstance = {
      id,
      sharedValue,
      priority,
      type,
      startTime: Date.now(),
    };

    // If we're at the max concurrent animations, queue it
    if (this.activeAnimations.size >= PERFORMANCE_CONFIG.maxConcurrentAnimations) {
      this.queueAnimation(animation);
      return;
    }

    this.startAnimation(animation);
  }

  /**
   * Cancel an animation by ID
   */
  cancelAnimation(id: string): void {
    const animation = this.activeAnimations.get(id);
    if (animation) {
      cancelAnimation(animation.sharedValue);
      this.activeAnimations.delete(id);
      this.processQueue();
    }
  }

  /**
   * Cancel all animations of a specific type
   */
  cancelAnimationsByType(type: 'spring' | 'timing' | 'gesture'): void {
    const animationsToCancel = Array.from(this.activeAnimations.values())
      .filter(animation => animation.type === type);

    animationsToCancel.forEach(animation => {
      this.cancelAnimation(animation.id);
    });
  }

  /**
   * Cancel all low priority animations
   */
  cancelLowPriorityAnimations(): void {
    const lowPriorityAnimations = Array.from(this.activeAnimations.values())
      .filter(animation => animation.priority === 'low');

    lowPriorityAnimations.forEach(animation => {
      this.cancelAnimation(animation.id);
    });
  }

  /**
   * Get the number of active animations
   */
  getActiveAnimationCount(): number {
    return this.activeAnimations.size;
  }

  /**
   * Check if a specific animation is active
   */
  isAnimationActive(id: string): boolean {
    return this.activeAnimations.has(id);
  }

  /**
   * Start an animation and track it
   */
  private startAnimation(animation: AnimationInstance): void {
    this.activeAnimations.set(animation.id, animation);
    
    // Set up cleanup when animation completes
    runOnJS(this.setupAnimationCleanup)(animation.id);
  }

  /**
   * Queue an animation for later execution
   */
  private queueAnimation(animation: AnimationInstance): void {
    // Remove any existing queued animation with the same ID
    this.animationQueue = this.animationQueue.filter(
      queuedAnimation => queuedAnimation.id !== animation.id
    );

    // Insert based on priority
    const insertIndex = this.findInsertIndex(animation.priority);
    this.animationQueue.splice(insertIndex, 0, animation);
  }

  /**
   * Find the correct index to insert an animation based on priority
   */
  private findInsertIndex(priority: 'low' | 'medium' | 'high'): number {
    const priorityOrder = { high: 3, medium: 2, low: 1 };
    const targetPriority = priorityOrder[priority];

    for (let i = 0; i < this.animationQueue.length; i++) {
      const queuedPriority = priorityOrder[this.animationQueue[i].priority];
      if (targetPriority > queuedPriority) {
        return i;
      }
    }

    return this.animationQueue.length;
  }

  /**
   * Process the animation queue
   */
  private processQueue(): void {
    if (this.isProcessingQueue || this.animationQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;

    while (
      this.animationQueue.length > 0 &&
      this.activeAnimations.size < PERFORMANCE_CONFIG.maxConcurrentAnimations
    ) {
      const nextAnimation = this.animationQueue.shift();
      if (nextAnimation) {
        this.startAnimation(nextAnimation);
      }
    }

    this.isProcessingQueue = false;
  }

  /**
   * Set up cleanup for when an animation completes
   */
  private setupAnimationCleanup(animationId: string): void {
    // This will be called from the JS thread
    setTimeout(() => {
      if (this.activeAnimations.has(animationId)) {
        this.activeAnimations.delete(animationId);
        this.processQueue();
      }
    }, 100); // Small delay to ensure animation has completed
  }

  /**
   * Clean up old animations that might have been orphaned
   */
  private cleanupOrphanedAnimations(): void {
    const now = Date.now();
    const maxAge = 5000; // 5 seconds

    const orphanedAnimations = Array.from(this.activeAnimations.values())
      .filter(animation => now - animation.startTime > maxAge);

    orphanedAnimations.forEach(animation => {
      this.cancelAnimation(animation.id);
    });
  }

  /**
   * Start periodic cleanup
   */
  startPeriodicCleanup(): void {
    setInterval(() => {
      this.cleanupOrphanedAnimations();
    }, 10000); // Clean up every 10 seconds
  }

  /**
   * Reset the animation manager (useful for testing)
   */
  reset(): void {
    // Cancel all active animations
    Array.from(this.activeAnimations.keys()).forEach(id => {
      this.cancelAnimation(id);
    });

    // Clear the queue
    this.animationQueue = [];
    this.isProcessingQueue = false;
  }
}

export default AnimationManager;
