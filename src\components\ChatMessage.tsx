import React from 'react';
import { View, StyleSheet } from 'react-native';
import { ChatMessage as ChatMessageType } from '../types';
import { useTheme } from '../theme';
import { Text } from './Text';
import { FadeInView } from './FadeInView';

interface ChatMessageProps {
  message: ChatMessageType;
}

export const ChatMessage: React.FC<ChatMessageProps> = ({ message }) => {
  const { theme } = useTheme();
  const isUser = message.sender === 'user';
  const isSystem = message.type === 'system';

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  if (isSystem) {
    return (
      <FadeInView direction="up" style={styles.systemContainer}>
        <View style={[
          styles.systemBubble,
          { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }
        ]}>
          <Text variant="caption" color="textSecondary" style={styles.systemText}>
            {message.text}
          </Text>
        </View>
      </FadeInView>
    );
  }

  return (
    <FadeInView
      direction={isUser ? 'right' : 'left'}
      style={[styles.container, isUser ? styles.userContainer : styles.assistantContainer]}>
      <View style={[
        styles.bubble,
        isUser ? [styles.userBubble, { backgroundColor: theme.colors.primary }] : [styles.assistantBubble, { backgroundColor: theme.colors.surface }]
      ]}>
        <Text
          variant="body"
          style={[
            styles.messageText,
            { color: isUser ? '#FFFFFF' : theme.colors.text }
          ]}>
          {message.text}
        </Text>
        
        {message.type === 'file_reference' && message.metadata?.fileReference && (
          <View style={[
            styles.fileReference,
            { backgroundColor: isUser ? 'rgba(255,255,255,0.2)' : theme.colors.border }
          ]}>
            <Text
              variant="caption"
              style={[
                styles.fileReferenceText,
                { color: isUser ? '#FFFFFF' : theme.colors.textSecondary }
              ]}>
              📁 {message.metadata.fileReference.split('/').pop()}
            </Text>
          </View>
        )}
        
        <Text
          variant="caption"
          style={[
            styles.timestamp,
            { color: isUser ? 'rgba(255,255,255,0.8)' : theme.colors.textSecondary }
          ]}>
          {formatTime(message.timestamp)}
        </Text>
      </View>
    </FadeInView>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 4,
    paddingHorizontal: 16,
  },
  userContainer: {
    alignItems: 'flex-end',
  },
  assistantContainer: {
    alignItems: 'flex-start',
  },
  bubble: {
    maxWidth: '80%',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 20,
  },
  userBubble: {
    borderBottomRightRadius: 4,
  },
  assistantBubble: {
    borderBottomLeftRadius: 4,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
  },
  messageText: {
    lineHeight: 20,
  },
  timestamp: {
    marginTop: 4,
    fontSize: 11,
  },
  fileReference: {
    marginTop: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  fileReferenceText: {
    fontSize: 12,
  },
  systemContainer: {
    alignItems: 'center',
    marginVertical: 8,
  },
  systemBubble: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    borderWidth: 1,
  },
  systemText: {
    textAlign: 'center',
    fontStyle: 'italic',
  },
});