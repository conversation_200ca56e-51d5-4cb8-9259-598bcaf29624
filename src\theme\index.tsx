import React, { createContext, useContext, useState, useEffect } from 'react';
import { Appearance, ColorSchemeName } from 'react-native';
import { colors, typography, spacing, radii, elevation, animation, hitTargets } from '../design-system/tokens';

export type ThemeMode = 'light' | 'dark' | 'system';

// Enhanced Theme Interface
export interface Theme {
  colors: typeof colors.light;
  typography: typeof typography;
  spacing: typeof spacing;
  radii: typeof radii;
  elevation: typeof elevation;
  animation: typeof animation;
  hitTargets: typeof hitTargets;
  mode: 'light' | 'dark';
}

const createTheme = (mode: 'light' | 'dark'): Theme => ({
  colors: colors[mode],
  typography,
  spacing,
  radii,
  elevation,
  animation,
  hitTargets,
  mode,
});

// Create theme instances using the design tokens
export const lightTheme = createTheme('light');
export const darkTheme = createTheme('dark');

// Enhanced Theme Context
interface ThemeContextType {
  theme: Theme;
  isDark: boolean;
  themeMode: ThemeMode;
  toggleTheme: () => void;
  setThemeMode: (mode: ThemeMode) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Theme Provider Component
interface ThemeProviderProps {
  children: React.ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [themeMode, setThemeMode] = useState<ThemeMode>('system');
  const [systemColorScheme, setSystemColorScheme] = useState<ColorSchemeName>(
    Appearance.getColorScheme()
  );

  useEffect(() => {
    const subscription = Appearance.addChangeListener(({ colorScheme }) => {
      setSystemColorScheme(colorScheme);
    });

    return () => subscription?.remove();
  }, []);

  const isDark = themeMode === 'dark' || (themeMode === 'system' && systemColorScheme === 'dark');

  const toggleTheme = () => {
    setThemeMode(isDark ? 'light' : 'dark');
  };

  const theme = isDark ? darkTheme : lightTheme;

  return (
    <ThemeContext.Provider value={{
      theme,
      isDark,
      themeMode,
      toggleTheme,
      setThemeMode
    }}>
      {children}
    </ThemeContext.Provider>
  );
};

// Theme Hook
export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

// Utility Functions
export const getResponsiveSize = (size: number, screenWidth: number): number => {
  const baseWidth = 375; // iPhone X width as base
  return Math.round((size * screenWidth) / baseWidth);
};

export const createShadow = (elevation: number, isDark: boolean) => {
  if (isDark) {
    return {
      shadowColor: '#000000',
      shadowOffset: {
        width: 0,
        height: elevation,
      },
      shadowOpacity: 0.3,
      shadowRadius: elevation * 1.5,
      elevation: elevation,
    };
  }
  
  return {
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: elevation,
    },
    shadowOpacity: 0.1,
    shadowRadius: elevation * 1.5,
    elevation: elevation,
  };
};