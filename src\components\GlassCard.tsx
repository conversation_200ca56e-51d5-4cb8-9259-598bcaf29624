import React from 'react';
import { View, ViewStyle, StyleSheet } from 'react-native';
import { BlurView } from 'expo-blur';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  interpolate,
} from 'react-native-reanimated';
import { useTheme } from '../theme';

interface GlassCardProps {
  children: React.ReactNode;
  style?: ViewStyle;
  intensity?: number;
  tint?: 'light' | 'dark' | 'default';
  borderRadius?: number;
  padding?: number;
  onPress?: () => void;
  pressable?: boolean;
  elevation?: number;
}

const AnimatedBlurView = Animated.createAnimatedComponent(BlurView);

export const GlassCard: React.FC<GlassCardProps> = ({
  children,
  style,
  intensity = 20,
  tint = 'light',
  borderRadius = 16,
  padding = 16,
  onPress,
  pressable = false,
  elevation = 2,
}) => {
  const { theme, isDark } = useTheme();
  const scale = useSharedValue(1);
  const pressed = useSharedValue(0);

  const handlePressIn = () => {
    if (pressable) {
      scale.value = withSpring(0.98, { damping: 15, stiffness: 300 });
      pressed.value = withSpring(1, { damping: 15, stiffness: 300 });
    }
  };

  const handlePressOut = () => {
    if (pressable) {
      scale.value = withSpring(1, { damping: 15, stiffness: 300 });
      pressed.value = withSpring(0, { damping: 15, stiffness: 300 });
    }
  };

  const animatedStyle = useAnimatedStyle(() => {
    const shadowElevation = interpolate(
      pressed.value,
      [0, 1],
      [elevation, elevation + 2]
    );

    return {
      transform: [{ scale: scale.value }],
      shadowOpacity: interpolate(shadowElevation, [0, 10], [0.1, 0.3]),
      shadowRadius: interpolate(shadowElevation, [0, 10], [4, 16]),
      elevation: shadowElevation,
    };
  });

  const cardStyle: ViewStyle = {
    borderRadius,
    padding,
    backgroundColor: isDark 
      ? 'rgba(255, 255, 255, 0.05)' 
      : 'rgba(255, 255, 255, 0.25)',
    borderWidth: 1,
    borderColor: isDark 
      ? 'rgba(255, 255, 255, 0.1)' 
      : 'rgba(255, 255, 255, 0.3)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: elevation },
    shadowOpacity: 0.1,
    shadowRadius: elevation * 2,
    overflow: 'hidden',
  };

  const Component = pressable ? Animated.createAnimatedComponent(View) : AnimatedBlurView;

  if (pressable && onPress) {
    return (
      <AnimatedBlurView
        intensity={intensity}
        tint={tint}
        style={[animatedStyle, cardStyle, style]}
      >
        <Animated.View
          style={styles.pressableContent}
          onTouchStart={handlePressIn}
          onTouchEnd={handlePressOut}
          onPress={onPress}
        >
          {children}
        </Animated.View>
      </AnimatedBlurView>
    );
  }

  return (
    <AnimatedBlurView
      intensity={intensity}
      tint={tint}
      style={[animatedStyle, cardStyle, style]}
    >
      {children}
    </AnimatedBlurView>
  );
};

const styles = StyleSheet.create({
  pressableContent: {
    flex: 1,
  },
});
