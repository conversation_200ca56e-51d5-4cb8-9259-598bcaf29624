# Development Build Installation Guide

## What is a Development Build?

A development build is a custom version of your app that includes the Expo development client. Unlike Expo Go, it has full access to native device features including:

- ✅ Full media library access (photos, videos, audio)
- ✅ All device permissions
- ✅ Custom native modules
- ✅ Production-like environment

## Current Build Status

Your build is currently in the queue: https://expo.dev/accounts/sivanithish/projects/file-manager-chat-app/builds/c15564ba-3fbe-4e57-a04c-e0af29fe2011

**Estimated completion time**: ~100 minutes (free tier queue)

## Installation Steps (When Build Completes)

### Step 1: Download the APK
1. Wait for the build to complete
2. You'll receive a download link for the APK file
3. Download the APK to your computer or directly to your Android device

### Step 2: Enable Unknown Sources (Android)
1. Go to **Settings** > **Security** (or **Privacy**)
2. Enable **"Install unknown apps"** or **"Unknown sources"**
3. Allow installation from your browser or file manager

### Step 3: Install the APK
1. If downloaded to computer: Transfer APK to your Android device
2. Open the APK file on your Android device
3. Follow the installation prompts
4. Grant any requested permissions

### Step 4: Connect to Development Server
1. Make sure your computer and Android device are on the same WiFi network
2. Start your development server:
   ```bash
   npx expo start --dev-client
   ```
3. Open the installed development build app on your device
4. Scan the QR code or enter the development server URL

## Testing Media Library Access

Once connected, test these features that weren't working in Expo Go:

### ✅ Photos Access
- Navigate to the file manager
- Try accessing the Photos folder
- Verify you can see actual photos from your device
- Test photo selection and preview

### ✅ Videos Access  
- Navigate to the Videos folder
- Verify you can see actual videos from your device
- Test video selection and playback

### ✅ Audio Access
- Navigate to the Audio/Music folder
- Verify you can see actual audio files from your device
- Test audio file selection and playback

### ✅ File Operations
- Test file copying, moving, deleting
- Test file sharing functionality
- Test file search across media files

## Expected Differences from Expo Go

| Feature | Expo Go | Development Build |
|---------|---------|-------------------|
| Photos Access | ❌ Limited/None | ✅ Full Access |
| Videos Access | ❌ Limited/None | ✅ Full Access |
| Audio Access | ❌ Limited/None | ✅ Full Access |
| File Permissions | ❌ Restricted | ✅ Full Access |
| Media Indexing | ❌ Fails | ✅ Works |
| Search Functionality | ❌ Limited | ✅ Full |

## Troubleshooting

### If the app crashes on startup:
1. Check device logs: `adb logcat`
2. Ensure all permissions are granted
3. Restart the development server

### If media files don't appear:
1. Grant storage permissions when prompted
2. Check if files exist in the expected locations
3. Restart the app after granting permissions

### If you can't connect to development server:
1. Ensure both devices are on same WiFi
2. Check firewall settings
3. Try using the device's IP address directly

## Performance Testing

With the development build, you can also test:
- ✅ Azure OpenAI integration (should work properly now)
- ✅ File search and indexing performance
- ✅ Large file handling
- ✅ Background file operations

## Next Steps After Testing

1. **Verify all media access works correctly**
2. **Test Azure OpenAI chat functionality**
3. **Performance test with large media libraries**
4. **Test file operations (copy, move, delete)**
5. **Validate search functionality across all file types**

## Build Information

- **Platform**: Android
- **Profile**: Development
- **Build ID**: c15564ba-3fbe-4e57-a04c-e0af29fe2011
- **Project**: @sivanithish/file-manager-chat-app
- **EAS Project ID**: 6a829bb3-baff-4f69-be87-c7a49b6c985f

## Support

If you encounter any issues:
1. Check the build logs at the provided URL
2. Review the troubleshooting section above
3. Ensure your Android device meets minimum requirements
4. Contact support if build fails
