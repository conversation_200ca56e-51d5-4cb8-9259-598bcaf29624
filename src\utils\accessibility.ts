import { AccessibilityRole, AccessibilityState } from 'react-native';

/**
 * Accessibility utilities for the enhanced file manager app
 */

export interface AccessibilityProps {
  accessible?: boolean;
  accessibilityLabel?: string;
  accessibilityHint?: string;
  accessibilityRole?: AccessibilityRole;
  accessibilityState?: AccessibilityState;
  accessibilityValue?: {
    min?: number;
    max?: number;
    now?: number;
    text?: string;
  };
}

/**
 * Create accessibility props for buttons
 */
export const createButtonAccessibility = (
  label: string,
  hint?: string,
  disabled?: boolean
): AccessibilityProps => ({
  accessible: true,
  accessibilityLabel: label,
  accessibilityHint: hint,
  accessibilityRole: 'button',
  accessibilityState: { disabled: !!disabled },
});

/**
 * Create accessibility props for text elements
 */
export const createTextAccessibility = (
  label: string,
  hint?: string
): AccessibilityProps => ({
  accessible: true,
  accessibilityLabel: label,
  accessibilityHint: hint,
  accessibilityRole: 'text',
});

/**
 * Create accessibility props for images
 */
export const createImageAccessibility = (
  label: string,
  hint?: string
): AccessibilityProps => ({
  accessible: true,
  accessibilityLabel: label,
  accessibilityHint: hint,
  accessibilityRole: 'image',
});

/**
 * Create accessibility props for progress indicators
 */
export const createProgressAccessibility = (
  label: string,
  value: number,
  max: number,
  hint?: string
): AccessibilityProps => ({
  accessible: true,
  accessibilityLabel: label,
  accessibilityHint: hint,
  accessibilityRole: 'progressbar',
  accessibilityValue: {
    min: 0,
    max,
    now: value,
    text: `${Math.round((value / max) * 100)}%`,
  },
});

/**
 * Create accessibility props for list items
 */
export const createListItemAccessibility = (
  label: string,
  hint?: string,
  selected?: boolean
): AccessibilityProps => ({
  accessible: true,
  accessibilityLabel: label,
  accessibilityHint: hint,
  accessibilityRole: 'button',
  accessibilityState: { selected: !!selected },
});

/**
 * Create accessibility props for navigation elements
 */
export const createNavigationAccessibility = (
  label: string,
  hint?: string,
  selected?: boolean
): AccessibilityProps => ({
  accessible: true,
  accessibilityLabel: label,
  accessibilityHint: hint,
  accessibilityRole: 'tab',
  accessibilityState: { selected: !!selected },
});

/**
 * Format file size for screen readers
 */
export const formatFileSizeForAccessibility = (bytes: number): string => {
  if (bytes === 0) return 'Empty file';
  
  const k = 1024;
  const sizes = ['bytes', 'kilobytes', 'megabytes', 'gigabytes'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  const size = parseFloat((bytes / Math.pow(k, i)).toFixed(1));
  
  return `${size} ${sizes[i]}`;
};

/**
 * Format date for screen readers
 */
export const formatDateForAccessibility = (date: Date): string => {
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  
  if (diffDays === 0) {
    return `Today at ${date.toLocaleTimeString([], { 
      hour: 'numeric', 
      minute: '2-digit' 
    })}`;
  } else if (diffDays === 1) {
    return `Yesterday at ${date.toLocaleTimeString([], { 
      hour: 'numeric', 
      minute: '2-digit' 
    })}`;
  } else if (diffDays < 7) {
    return `${diffDays} days ago`;
  } else {
    return date.toLocaleDateString([], {
      month: 'long',
      day: 'numeric',
      year: 'numeric',
    });
  }
};

/**
 * Create storage usage accessibility description
 */
export const createStorageAccessibilityDescription = (
  used: number,
  total: number
): string => {
  const percentage = Math.round((used / total) * 100);
  return `Storage usage: ${used} gigabytes used of ${total} gigabytes total, ${percentage} percent full`;
};

/**
 * Minimum hit target size for accessibility
 */
export const MIN_HIT_TARGET = 44;

/**
 * Check if a component meets minimum hit target requirements
 */
export const meetsHitTargetRequirements = (
  width: number,
  height: number
): boolean => {
  return width >= MIN_HIT_TARGET && height >= MIN_HIT_TARGET;
};

/**
 * Ensure minimum hit target size
 */
export const ensureHitTarget = (size: number): number => {
  return Math.max(size, MIN_HIT_TARGET);
};
