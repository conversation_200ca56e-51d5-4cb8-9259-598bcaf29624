import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  StatusBar,
  Dimensions,
} from 'react-native';
import { useTheme } from '../theme';
import { Text } from '../components/Text';
import { 
  Card, 
  IconButton, 
  Chip, 
  MultiRingProgress 
} from '../components/design-system';
import { Icon, IconNames } from '../components/Icon';
import { DeviceFileSystemService } from '../services/DeviceFileSystemService';

const { width: screenWidth } = Dimensions.get('window');

interface EnhancedStatisticsScreenProps {
  onNavigateBack?: () => void;
}

export const EnhancedStatisticsScreen: React.FC<EnhancedStatisticsScreenProps> = ({
  onNavigateBack,
}) => {
  const { theme } = useTheme();
  const [storageData, setStorageData] = useState({
    total: 860,
    used: 432,
    pictures: 219,
    documents: 165,
    videos: 48,
    picturesCount: 1685,
    documentsCount: 1160,
    videosCount: 89,
  });

  useEffect(() => {
    loadStorageData();
  }, []);

  const loadStorageData = async () => {
    try {
      const deviceService = DeviceFileSystemService.getInstance();
      const info = await deviceService.getDeviceStorageInfo();
      if (info) {
        // For demo purposes, using the spec data
        // In real app, this would come from actual file analysis
        setStorageData({
          total: 860,
          used: 432,
          pictures: 219,
          documents: 165,
          videos: 48,
          picturesCount: 1685,
          documentsCount: 1160,
          videosCount: 89,
        });
      }
    } catch (error) {
      console.error('Failed to load storage data:', error);
    }
  };

  const usagePercentage = Math.round((storageData.used / storageData.total) * 100);

  const rings = [
    {
      value: storageData.used,
      total: storageData.total,
      color: theme.colors.dataColors.pink,
      thickness: 16,
      label: 'Total',
    },
    {
      value: storageData.pictures,
      total: storageData.total,
      color: theme.colors.dataColors.pink,
      thickness: 14,
      label: 'Pictures',
    },
    {
      value: storageData.documents,
      total: storageData.total,
      color: theme.colors.dataColors.yellow,
      thickness: 14,
      label: 'Documents',
    },
    {
      value: storageData.videos,
      total: storageData.total,
      color: theme.colors.dataColors.blue,
      thickness: 14,
      label: 'Videos',
    },
  ];

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      {/* Header */}
      <View style={styles.header}>
        <IconButton
          icon={IconNames.BACK}
          onPress={onNavigateBack}
          variant="ghost"
          style={styles.backButton}
        />
      </View>

      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Promo Banner */}
        <Card style={[styles.promoBanner, { backgroundColor: theme.colors.surfaceAlt }]} elevation="x1">
          <View style={styles.promoContent}>
            <View style={styles.promoLeft}>
              <View style={styles.robotOrb} />
              <View style={styles.promoText}>
                <Text variant="body" color="text" weight="medium">
                  Optimize your space!
                </Text>
                <Text variant="caption" color="textSecondary">
                  See our tutorial.
                </Text>
              </View>
            </View>
            <Icon name={IconNames.VIDEO} size={20} color={theme.colors.textSecondary} />
          </View>
        </Card>

        {/* Title Row */}
        <View style={styles.titleRow}>
          <View style={styles.titleLeft}>
            <Text style={[
              theme.typography.title.large,
              { color: theme.colors.text }
            ]}>
              Statistics
            </Text>
            <Text variant="body" color="textTertiary" style={styles.subtitle}>
              08/12/23
            </Text>
          </View>
          
          <Chip
            label={`${usagePercentage}% Used`}
            icon={IconNames.INFO}
            variant="secondary"
            style={[styles.usageBadge, { backgroundColor: 'rgba(255,255,255,0.06)' }]}
          />
        </View>

        {/* Radial Gauge Card */}
        <Card style={[styles.gaugeCard, { backgroundColor: theme.colors.card }]} elevation="x2">
          <View style={styles.gaugeContainer}>
            <MultiRingProgress
              rings={rings}
              size={240}
              animated={true}
              style={styles.gauge}
            >
              {/* Center Label */}
              <View style={styles.centerLabel}>
                <Text style={[
                  theme.typography.display.large,
                  { color: theme.colors.text }
                ]}>
                  {storageData.used}
                </Text>
                <Text variant="body" color="textSecondary">
                  GB
                </Text>
              </View>
            </MultiRingProgress>
          </View>

          {/* Legend */}
          <View style={styles.legend}>
            <View style={styles.legendRow}>
              <View style={styles.legendItem}>
                <View style={[styles.legendDot, { backgroundColor: theme.colors.dataColors.pink }]} />
                <View style={styles.legendText}>
                  <Text variant="body" color="text" weight="medium">
                    Pictures
                  </Text>
                  <Text variant="caption" color="textSecondary">
                    {storageData.picturesCount.toLocaleString()} files · {storageData.pictures}GB
                  </Text>
                </View>
              </View>
            </View>

            <View style={styles.legendRow}>
              <View style={styles.legendItem}>
                <View style={[styles.legendDot, { backgroundColor: theme.colors.dataColors.yellow }]} />
                <View style={styles.legendText}>
                  <Text variant="body" color="text" weight="medium">
                    Documents
                  </Text>
                  <Text variant="caption" color="textSecondary">
                    {storageData.documentsCount.toLocaleString()} files · {storageData.documents}GB
                  </Text>
                </View>
              </View>
            </View>

            <View style={styles.legendRow}>
              <View style={styles.legendItem}>
                <View style={[styles.legendDot, { backgroundColor: theme.colors.dataColors.blue }]} />
                <View style={styles.legendText}>
                  <Text variant="body" color="text" weight="medium">
                    Videos
                  </Text>
                  <Text variant="caption" color="textSecondary">
                    {storageData.videosCount.toLocaleString()} files · {storageData.videos}GB
                  </Text>
                </View>
              </View>
            </View>
          </View>
        </Card>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  backButton: {
    alignSelf: 'flex-start',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
  promoBanner: {
    marginBottom: 24,
    borderRadius: 18,
  },
  promoContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  promoLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  robotOrb: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    marginRight: 12,
  },
  promoText: {
    flex: 1,
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 24,
  },
  titleLeft: {
    flex: 1,
  },
  subtitle: {
    marginTop: 4,
  },
  usageBadge: {
    borderRadius: 12,
  },
  gaugeCard: {
    borderRadius: 24,
    padding: 24,
  },
  gaugeContainer: {
    alignItems: 'center',
    marginBottom: 32,
  },
  gauge: {
    marginVertical: 20,
  },
  centerLabel: {
    alignItems: 'center',
  },
  legend: {
    gap: 16,
  },
  legendRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  legendDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  legendText: {
    flex: 1,
  },
});
