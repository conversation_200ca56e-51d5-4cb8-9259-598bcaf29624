import React, { useRef, useEffect } from 'react';
import { FlatList, StyleSheet, View } from 'react-native';
import { ChatMessage as ChatMessageType } from '../types';
import { useTheme } from '../theme';
import { ChatMessage } from './ChatMessage';
import { Text } from './Text';
import { FadeInView } from './FadeInView';

interface ChatHistoryProps {
  messages: ChatMessageType[];
  loading?: boolean;
}

export const ChatHistory: React.FC<ChatHistoryProps> = ({
  messages,
  loading = false,
}) => {
  const { theme } = useTheme();
  const flatListRef = useRef<FlatList>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messages.length > 0) {
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [messages.length]);

  const renderMessage = ({ item }: { item: ChatMessageType; index: number }) => (
    <ChatMessage message={item} />
  );

  const renderEmptyState = () => (
    <FadeInView style={styles.emptyState}>
      <Text variant="body" color="textSecondary" style={styles.emptyText}>
        Start a conversation! Ask me about file management, searching, or organizing your files.
      </Text>
    </FadeInView>
  );

  const renderLoadingIndicator = () => {
    if (!loading) return null;

    return (
      <FadeInView style={styles.loadingContainer}>
        <View style={[styles.loadingBubble, { backgroundColor: theme.colors.surface }]}>
          <View style={styles.typingIndicator}>
            <View style={[styles.dot, { backgroundColor: theme.colors.textSecondary }]} />
            <View style={[styles.dot, { backgroundColor: theme.colors.textSecondary }]} />
            <View style={[styles.dot, { backgroundColor: theme.colors.textSecondary }]} />
          </View>
        </View>
      </FadeInView>
    );
  };

  return (
    <View style={styles.container}>
      <FlatList
        ref={flatListRef}
        data={messages}
        keyExtractor={(item) => item.id}
        renderItem={renderMessage}
        contentContainerStyle={[
          styles.contentContainer,
          messages.length === 0 && styles.emptyContainer,
        ]}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={renderEmptyState}
        maintainVisibleContentPosition={{
          minIndexForVisible: 0,
          autoscrollToTopThreshold: 10,
        }}
      />
      {renderLoadingIndicator()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    paddingVertical: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  emptyState: {
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyText: {
    textAlign: 'center',
    lineHeight: 24,
  },
  loadingContainer: {
    alignItems: 'flex-start',
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  loadingBubble: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 20,
    borderBottomLeftRadius: 4,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
  },
  typingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  dot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    opacity: 0.6,
  },
});