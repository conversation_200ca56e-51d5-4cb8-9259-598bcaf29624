import React, { useState } from 'react';
import { View, StyleSheet, Animated } from 'react-native';
import { useTheme } from '../theme';
import { EnhancedHomeScreen } from '../screens/EnhancedHomeScreen';
import { EnhancedStatisticsScreen } from '../screens/EnhancedStatisticsScreen';
import { EnhancedFolderScreen } from '../screens/EnhancedFolderScreen';
import { EnhancedChatScreen } from '../screens/EnhancedChatScreen';
import { DeviceFileManagerScreen } from '../screens/DeviceFileManagerScreen';

type Screen = 'home' | 'statistics' | 'folder' | 'chat' | 'files' | 'settings';

interface NavigationState {
  currentScreen: Screen;
  previousScreen?: Screen;
  animating: boolean;
}

export const EnhancedAppNavigator: React.FC = () => {
  const { theme } = useTheme();
  const [navigationState, setNavigationState] = useState<NavigationState>({
    currentScreen: 'home',
    animating: false,
  });

  const slideAnimation = React.useRef(new Animated.Value(0)).current;

  const navigateToScreen = (screen: Screen) => {
    if (navigationState.animating || navigationState.currentScreen === screen) {
      return;
    }

    setNavigationState(prev => ({
      ...prev,
      previousScreen: prev.currentScreen,
      animating: true,
    }));

    // Spring animation for screen transitions
    Animated.spring(slideAnimation, {
      toValue: 1,
      damping: 20,
      stiffness: 160,
      useNativeDriver: true,
    }).start(() => {
      setNavigationState(prev => ({
        currentScreen: screen,
        previousScreen: undefined,
        animating: false,
      }));
      slideAnimation.setValue(0);
    });
  };

  const navigateBack = () => {
    const backScreenMap: Record<Screen, Screen> = {
      statistics: 'home',
      folder: 'home',
      chat: 'home',
      files: 'home',
      settings: 'home',
      home: 'home',
    };

    const targetScreen = backScreenMap[navigationState.currentScreen];
    navigateToScreen(targetScreen);
  };

  const renderCurrentScreen = () => {
    const screenProps = {
      onNavigateToStatistics: () => navigateToScreen('statistics'),
      onNavigateToFolder: () => navigateToScreen('folder'),
      onNavigateToDeviceFiles: () => navigateToScreen('files'),
      onNavigateToChat: () => navigateToScreen('chat'),
      onNavigateToSettings: () => navigateToScreen('settings'),
      onNavigateBack: navigateBack,
    };

    switch (navigationState.currentScreen) {
      case 'home':
        return <EnhancedHomeScreen {...screenProps} />;
      
      case 'statistics':
        return <EnhancedStatisticsScreen onNavigateBack={navigateBack} />;
      
      case 'folder':
        return <EnhancedFolderScreen onNavigateBack={navigateBack} />;
      
      case 'chat':
        return <EnhancedChatScreen onNavigateBack={navigateBack} />;
      
      case 'files':
        return (
          <DeviceFileManagerScreen 
            onNavigateBack={navigateBack}
            onNavigateToChat={() => navigateToScreen('chat')}
          />
        );
      
      case 'settings':
        return <EnhancedHomeScreen {...screenProps} />; // Placeholder
      
      default:
        return <EnhancedHomeScreen {...screenProps} />;
    }
  };

  const getScreenTransform = () => {
    if (!navigationState.animating) {
      return { transform: [{ translateX: 0 }] };
    }

    return {
      transform: [
        {
          translateX: slideAnimation.interpolate({
            inputRange: [0, 1],
            outputRange: [0, -100],
          }),
        },
      ],
    };
  };

  const getNextScreenTransform = () => {
    if (!navigationState.animating) {
      return { transform: [{ translateX: 1000 }] };
    }

    return {
      transform: [
        {
          translateX: slideAnimation.interpolate({
            inputRange: [0, 1],
            outputRange: [100, 0],
          }),
        },
      ],
    };
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Current Screen */}
      <Animated.View style={[styles.screen, getScreenTransform()]}>
        {renderCurrentScreen()}
      </Animated.View>

      {/* Next Screen (during transition) */}
      {navigationState.animating && navigationState.previousScreen && (
        <Animated.View style={[styles.screen, styles.nextScreen, getNextScreenTransform()]}>
          {renderCurrentScreen()}
        </Animated.View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  screen: {
    flex: 1,
  },
  nextScreen: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
});
