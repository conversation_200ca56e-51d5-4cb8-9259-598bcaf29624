import React, { useState, useEffect } from 'react';
import { View, StyleSheet, KeyboardAvoidingView, Platform } from 'react-native';
import { ChatInterfaceProps, ChatMessage } from '../types';
import { useTheme } from '../theme';
import { ChatService } from '../services/ChatService';
import { Text } from '../components/Text';

import { Icon, IconNames } from '../components/Icon';
import { ChatHistory } from '../components/ChatHistory';
import { MessageInput } from '../components/MessageInput';
import { ScaleOnPress } from '../components/ScaleOnPress';
import { FadeInView } from '../components/FadeInView';

export const ChatScreen: React.FC<ChatInterfaceProps> = ({
  isVisible,
  onClose,
  messages: externalMessages,
  onSendMessage: externalOnSendMessage,
}) => {
  const { theme } = useTheme();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [loading, setLoading] = useState(false);
  const [sendingMessage, setSendingMessage] = useState(false);

  const chatService = ChatService.getInstance();

  // Load message history on mount
  useEffect(() => {
    if (isVisible) {
      loadMessageHistory();
    }
  }, [isVisible]);

  // Use external messages if provided, otherwise use internal state
  const displayMessages = externalMessages || messages;

  const loadMessageHistory = async () => {
    try {
      setLoading(true);
      const history = await chatService.getMessageHistory();
      setMessages(history);
    } catch (error) {
      console.error('Failed to load message history:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSendMessage = async (messageText: string) => {
    try {
      setSendingMessage(true);

      if (externalOnSendMessage) {
        // Use external handler if provided
        externalOnSendMessage(messageText);
      } else {
        // Use internal chat service
        await chatService.sendMessage(messageText);

        // Reload message history to get both user message and response
        await loadMessageHistory();
      }
    } catch (error) {
      console.error('Failed to send message:', error);
    } finally {
      setSendingMessage(false);
    }
  };

  const handleClearHistory = async () => {
    try {
      await chatService.clearHistory();
      await loadMessageHistory();
    } catch (error) {
      console.error('Failed to clear history:', error);
    }
  };

  const renderHeader = () => (
    <View style={[styles.header, { backgroundColor: theme.colors.surface }]}>
      <View style={styles.headerContent}>
        <View style={styles.headerLeft}>
          <View style={[styles.avatarContainer, { backgroundColor: theme.colors.primary }]}>
            <Icon name={IconNames.CHAT} size={20} color="#FFFFFF" />
          </View>
          <View>
            <Text variant="body" color="text" weight="semibold">
              File Assistant
            </Text>
            <Text variant="caption" color="textSecondary">
              Always ready to help
            </Text>
          </View>
        </View>

        <View style={styles.headerActions}>
          <ScaleOnPress onPress={handleClearHistory}>
            <View style={[styles.headerButton, { backgroundColor: theme.colors.background }]}>
              <Icon name="clear" size={18} color={theme.colors.textSecondary} />
            </View>
          </ScaleOnPress>
          
          <ScaleOnPress onPress={onClose}>
            <View style={[styles.headerButton, { backgroundColor: theme.colors.background }]}>
              <Icon name="close" size={18} color={theme.colors.textSecondary} />
            </View>
          </ScaleOnPress>
        </View>
      </View>
    </View>
  );

  const renderQuickActions = () => (
    <FadeInView direction="up" style={styles.quickActions}>
      <Text variant="caption" color="textSecondary" style={styles.quickActionsTitle}>
        Quick Actions
      </Text>
      <View style={styles.quickActionButtons}>
        <ScaleOnPress onPress={() => handleSendMessage('Help me organize my files')}>
          <View style={[styles.quickActionButton, { backgroundColor: theme.colors.surface }]}>
            <Text variant="caption" color="primary" weight="medium">
              Organize Files
            </Text>
          </View>
        </ScaleOnPress>
        
        <ScaleOnPress onPress={() => handleSendMessage('How do I search for files?')}>
          <View style={[styles.quickActionButton, { backgroundColor: theme.colors.surface }]}>
            <Text variant="caption" color="primary" weight="medium">
              Search Help
            </Text>
          </View>
        </ScaleOnPress>
        
        <ScaleOnPress onPress={() => handleSendMessage('Show me recent files')}>
          <View style={[styles.quickActionButton, { backgroundColor: theme.colors.surface }]}>
            <Text variant="caption" color="primary" weight="medium">
              Recent Files
            </Text>
          </View>
        </ScaleOnPress>
      </View>
    </FadeInView>
  );

  if (!isVisible) {
    return null;
  }

  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}>
      
      {renderHeader()}
      
      <View style={styles.content}>
        <ChatHistory
          messages={displayMessages}
          loading={loading}
        />
        
        {displayMessages.length === 0 && !loading && renderQuickActions()}
      </View>
      
      <MessageInput
        onSendMessage={handleSendMessage}
        disabled={sendingMessage}
        placeholder={sendingMessage ? 'Sending...' : 'Ask me anything about your files...'}
      />
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 50,
    paddingBottom: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  avatarContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  headerButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
  },
  content: {
    flex: 1,
  },
  quickActions: {
    padding: 16,
    paddingTop: 32,
  },
  quickActionsTitle: {
    marginBottom: 12,
    textAlign: 'center',
  },
  quickActionButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    gap: 8,
  },
  quickActionButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
  },
});