import Constants from 'expo-constants';

/**
 * Read environment variables from multiple sources supported by Expo + Metro.
 * Priority: app.json/app.config extra -> process.env -> defaultValue
 */
export function env(key: string, defaultValue: string = ''): string {
  // Expo extra from app.json or app.config
  const extra = (Constants as any)?.expoConfig?.extra || (Constants as any)?.manifest?.extra || {};
  if (extra && typeof extra[key] === 'string' && extra[key]) {
    return String(extra[key]);
  }
  // Process env (works if Metro inlines envs)
  if (typeof process !== 'undefined' && process.env && process.env[key]) {
    return String(process.env[key]);
  }
  return defaultValue;
}

export interface AzureEnvConfig {
  apiKey: string;
  endpoint: string; // e.g. https://my-instance.openai.azure.com
  apiVersion: string;
  deployment: string; // Azure Deployment name (not model)
  maxTokens?: number;
  temperature?: number;
}

export function readAzureConfig(): AzureEnvConfig | null {
  // Support both DEPLOYMENT and MODEL var names (prefer DEPLOYMENT)
  const deployment = env('AZURE_OPENAI_DEPLOYMENT') || env('AZURE_OPENAI_MODEL');
  const cfg: AzureEnvConfig = {
    apiKey: env('AZURE_OPENAI_API_KEY'),
    endpoint: env('AZURE_OPENAI_ENDPOINT'),
    apiVersion: env('AZURE_OPENAI_API_VERSION', '2024-12-01-preview'),
    deployment: deployment,
    maxTokens: Number(env('AZURE_MAX_TOKENS', '4000')),
    temperature: Number(env('AZURE_TEMPERATURE', '0.1')),
  };

  if (!cfg.apiKey || !cfg.endpoint || !cfg.deployment) {
    return null;
  }
  return cfg;
}

export function parseAzureInstanceName(endpoint: string): string | null {
  try {
    const url = new URL(endpoint);
    // example host: my-instance.openai.azure.com
    const hostParts = url.hostname.split('.');
    const instance = hostParts[0];
    return instance || null;
  } catch (e) {
    return null;
  }
}

