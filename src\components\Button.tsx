import React from 'react';
import { ViewStyle, TextStyle } from 'react-native';
import { AnimatedButton } from './AnimatedButton';

interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  hapticFeedback?: boolean;
}

export const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  style,
  textStyle,
  hapticFeedback = true,
}) => {
  const handlePress = () => {
    if (!loading && !disabled) {
      onPress();
    }
  };

  const displayTitle = loading ? 'Loading...' : title;
  const isDisabled = disabled || loading;

  return (
    <AnimatedButton
      title={displayTitle}
      onPress={handlePress}
      variant={variant}
      size={size}
      disabled={isDisabled}
      style={style || undefined}
      textStyle={textStyle || undefined}
      hapticFeedback={hapticFeedback}
    />
  );
};