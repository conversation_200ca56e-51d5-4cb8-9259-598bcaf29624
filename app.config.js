import 'dotenv/config';

export default {
  expo: {
    name: "File Manager Chat App",
    slug: "file-manager-chat-app",
    version: "1.0.0",
    orientation: "portrait",
    userInterfaceStyle: "automatic",
    splash: {
      backgroundColor: "#ffffff"
    },
    assetBundlePatterns: [
      "**/*"
    ],
    ios: {
      supportsTablet: true,
      bundleIdentifier: "com.filemanager.chatapp"
    },
    android: {
      package: "com.filemanager.chatapp",
      permissions: [
        "READ_EXTERNAL_STORAGE",
        "WRITE_EXTERNAL_STORAGE",
        "READ_MEDIA_IMAGES",
        "READ_MEDIA_VIDEO",
        "READ_MEDIA_AUDIO",
        "CAMERA",
        "RECORD_AUDIO"
      ]
    },
    web: {
      bundler: "metro"
    },
    plugins: [
      "expo-secure-store",
      [
        "expo-media-library",
        {
          photosPermission: "Allow $(PRODUCT_NAME) to access your photos.",
          savePhotosPermission: "Allow $(PRODUCT_NAME) to save photos.",
          isAccessMediaLocationEnabled: true
        }
      ],
      [
        "expo-image-picker",
        {
          photosPermission: "The app accesses your photos to let you share them.",
          cameraPermission: "The app accesses your camera to let you take photos."
        }
      ]
    ],
    extra: {
      // EAS project configuration
      eas: {
        projectId: "6a829bb3-baff-4f69-be87-c7a49b6c985f"
      },
      // Pass environment variables to the app
      AZURE_OPENAI_API_KEY: process.env.AZURE_OPENAI_API_KEY,
      AZURE_OPENAI_ENDPOINT: process.env.AZURE_OPENAI_ENDPOINT,
      AZURE_OPENAI_API_VERSION: process.env.AZURE_OPENAI_API_VERSION,
      AZURE_OPENAI_DEPLOYMENT: process.env.AZURE_OPENAI_DEPLOYMENT,
      AZURE_MAX_TOKENS: process.env.AZURE_MAX_TOKENS,
      AZURE_TEMPERATURE: process.env.AZURE_TEMPERATURE,
    }
  }
};
