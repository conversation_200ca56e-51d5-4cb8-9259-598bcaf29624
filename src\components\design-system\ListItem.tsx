import React from 'react';
import { TouchableOpacity, View, ViewStyle, StyleSheet } from 'react-native';
import { useTheme } from '../../theme';
import { Text } from '../Text';
import { Icon, IconNames } from '../Icon';
import { Avatar } from './Avatar';

interface ListItemProps {
  title: string;
  subtitle?: string;
  leftIcon?: IconNames;
  leftImage?: { uri: string } | number;
  rightIcon?: IconNames;
  rightText?: string;
  onPress?: () => void;
  onLongPress?: () => void;
  selected?: boolean;
  disabled?: boolean;
  showDivider?: boolean;
  style?: ViewStyle;
  accentColor?: string;
}

export const ListItem: React.FC<ListItemProps> = ({
  title,
  subtitle,
  leftIcon,
  leftImage,
  rightIcon,
  rightText,
  onPress,
  onLongPress,
  selected = false,
  disabled = false,
  showDivider = false,
  style,
  accentColor,
}) => {
  const { theme } = useTheme();

  const getContainerStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      height: 64,
      paddingHorizontal: theme.spacing.lg,
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: selected 
        ? `${theme.colors.primary}08` // 4% opacity
        : 'transparent',
    };

    return baseStyle;
  };

  const renderLeftElement = () => {
    if (leftImage) {
      return (
        <Avatar 
          source={leftImage} 
          size="medium" 
          style={styles.leftElement}
        />
      );
    }
    
    if (leftIcon) {
      return (
        <View style={[styles.iconContainer, styles.leftElement]}>
          <Icon 
            name={leftIcon} 
            size={20} 
            color={theme.colors.textSecondary} 
          />
        </View>
      );
    }

    if (accentColor) {
      return (
        <View style={[
          styles.accentDot, 
          styles.leftElement,
          { backgroundColor: accentColor }
        ]} />
      );
    }

    return null;
  };

  const renderRightElement = () => {
    if (rightText) {
      return (
        <Text 
          variant="caption" 
          color="textSecondary" 
          style={styles.rightElement}
        >
          {rightText}
        </Text>
      );
    }

    if (rightIcon) {
      return (
        <Icon 
          name={rightIcon} 
          size={20} 
          color={theme.colors.textSecondary}
          style={styles.rightElement}
        />
      );
    }

    return null;
  };

  const content = (
    <View style={[getContainerStyle(), disabled && styles.disabled, style]}>
      {renderLeftElement()}
      
      <View style={styles.content}>
        <Text 
          variant="body" 
          color="text" 
          weight="medium"
          numberOfLines={1}
        >
          {title}
        </Text>
        {subtitle && (
          <Text 
            variant="caption" 
            color="textSecondary"
            numberOfLines={1}
            style={styles.subtitle}
          >
            {subtitle}
          </Text>
        )}
      </View>

      {renderRightElement()}

      {showDivider && (
        <View style={[
          styles.divider, 
          { backgroundColor: theme.colors.border }
        ]} />
      )}
    </View>
  );

  if (onPress && !disabled) {
    return (
      <TouchableOpacity 
        onPress={onPress}
        onLongPress={onLongPress}
        activeOpacity={0.7}
      >
        {content}
      </TouchableOpacity>
    );
  }

  return content;
};

const styles = StyleSheet.create({
  leftElement: {
    marginRight: 12,
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: 14,
    alignItems: 'center',
    justifyContent: 'center',
  },
  accentDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
  },
  subtitle: {
    marginTop: 2,
  },
  rightElement: {
    marginLeft: 12,
  },
  disabled: {
    opacity: 0.5,
  },
  divider: {
    position: 'absolute',
    bottom: 0,
    left: 64,
    right: 0,
    height: 1,
  },
});
