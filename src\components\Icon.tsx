import React from 'react';
import { View, Text, ViewStyle } from 'react-native';
import { useTheme } from '../theme';

// Icon names enum for type safety
export enum IconNames {
  // Navigation & UI
  FOLDER = 'folder',
  FILE = 'file',
  SEARCH = 'search',
  CHAT = 'chat',
  SETTINGS = 'settings',
  BACK = 'back',
  MORE = 'more',
  MORE_HORIZONTAL = 'more-horizontal',
  PLUS = 'plus',
  CREATE = 'create',
  CHECK = 'check',
  CLOSE = 'close',
  SEND = 'send',
  VIEW = 'view',
  REFRESH = 'refresh',
  INFO = 'info',

  // File types
  IMAGE = 'image',
  TEXT = 'text',
  AUDIO = 'audio',
  VIDEO = 'video',
  ARCHIVE = 'archive',
  FILE_TEXT = 'file-text',
  MUSIC = 'music',

  // Actions
  DELETE = 'delete',
  COPY = 'copy',
  MOVE = 'move',
  RENAME = 'rename',
  CLEAR = 'clear',
  DOWNLOAD = 'download',
  SHARE = 'share',

  // System
  HARD_DRIVE = 'hard-drive',
  CAMERA = 'camera',
  MICROPHONE = 'microphone',

  // Modern UI Icons
  GRID = 'grid',
  LIST = 'list',
  HEART = 'heart',
  STAR = 'star',
  BOOKMARK = 'bookmark',
  FILTER = 'filter',
  SORT = 'sort',
  UPLOAD = 'upload',
  CLOUD = 'cloud',
  WIFI = 'wifi',
  BATTERY = 'battery',
  NOTIFICATION = 'notification',
}

// Simple icon component using emoji/text
// In a real app, you'd use react-native-vector-icons or similar
interface IconProps {
  name: string;
  size?: number;
  color?: string;
  style?: ViewStyle;
}

export const Icon: React.FC<IconProps> = ({
  name,
  size = 24,
  color,
  style,
}) => {
  const { theme } = useTheme();
  const iconColor = color || theme.colors.text;

  // Map icon names to modern symbols/icons
  const iconMap: Record<string, string> = {
    // Navigation & UI
    folder: '📂',
    file: '📄',
    search: '🔍',
    chat: '💬',
    settings: '⚙️',
    back: '←',
    more: '⋯',
    'more-horizontal': '⋯',
    plus: '＋',
    create: '＋',
    check: '✓',
    close: '✕',
    send: '→',
    view: '👁',
    refresh: '↻',
    info: 'ⓘ',

    // File types
    image: '🖼',
    text: '📝',
    audio: '🎵',
    video: '🎬',
    archive: '📦',
    'file-text': '📄',
    music: '🎵',

    // Actions
    delete: '🗑',
    copy: '📋',
    move: '📦',
    rename: '✏',
    clear: '🗑',
    download: '⬇',
    share: '📤',

    // System
    'hard-drive': '💾',
    camera: '📷',
    microphone: '🎤',

    // Modern UI Icons
    grid: '⊞',
    list: '☰',
    heart: '♡',
    star: '☆',
    bookmark: '🔖',
    filter: '⚗',
    sort: '⇅',
    upload: '⬆',
    cloud: '☁',
    wifi: '📶',
    battery: '🔋',
    notification: '🔔',
  };

  const iconSymbol = iconMap[name] || '?';

  return (
    <View
      style={[
        {
          width: size,
          height: size,
          alignItems: 'center',
          justifyContent: 'center',
        },
        style,
      ]}>
      <Text
        style={{
          fontSize: size * 0.8,
          color: iconColor,
          textAlign: 'center',
        }}>
        {iconSymbol}
      </Text>
    </View>
  );
};