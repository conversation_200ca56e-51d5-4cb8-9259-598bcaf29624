import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Image,
  FlatList,
} from 'react-native';
import { useTheme } from '../theme';
import { Text } from '../components/Text';
import { Icon } from '../components/Icon';
import { FadeInView } from '../components/FadeInView';
import { FileItem } from '../types';

interface FolderViewScreenProps {
  onBack?: () => void;
  files?: FileItem[];
}

export const FolderViewScreen: React.FC<FolderViewScreenProps> = ({ 
  onBack,
  files = []
}) => {
  const { theme } = useTheme();
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);

  // Mock data for demo
  const mockFiles: FileItem[] = [
    {
      name: 'IMG2075.jpg',
      path: '/path/to/IMG2075.jpg',
      type: 'file',
      size: 2048000,
      modifiedDate: new Date('2023-12-08T03:38:00'),
      mimeType: 'image/jpeg',
      isHidden: false,
      permissions: { readable: true, writable: true, executable: false },
    },
    {
      name: 'IMG2074.jpg',
      path: '/path/to/IMG2074.jpg',
      type: 'file',
      size: 1856000,
      modifiedDate: new Date('2023-12-08T03:38:00'),
      mimeType: 'image/jpeg',
      isHidden: false,
      permissions: { readable: true, writable: true, executable: false },
    },
    {
      name: 'IMG2073.jpg',
      path: '/path/to/IMG2073.jpg',
      type: 'file',
      size: 1920000,
      modifiedDate: new Date('2023-12-08T03:38:00'),
      mimeType: 'image/jpeg',
      isHidden: false,
      permissions: { readable: true, writable: true, executable: false },
    },
  ];

  const displayFiles = files.length > 0 ? files : mockFiles;

  const toggleFileSelection = (filePath: string) => {
    setSelectedFiles(prev => 
      prev.includes(filePath) 
        ? prev.filter(path => path !== filePath)
        : [...prev, filePath]
    );
  };

  const FileListItem = ({ item, index }: { item: FileItem; index: number }) => {
    const isSelected = selectedFiles.includes(item.path);
    const isMiddleItem = index === 1; // Second item for demo
    
    return (
      <TouchableOpacity
        style={[
          styles.fileItem,
          { backgroundColor: theme.colors.card },
          isSelected && { backgroundColor: 'rgba(255, 119, 183, 0.04)' }
        ]}
        onPress={() => toggleFileSelection(item.path)}
        activeOpacity={0.8}
      >
        <View style={styles.fileItemContent}>
          <View style={[styles.fileThumbnail, { backgroundColor: '#FF8AD6' }]}>
            <Icon name="image" size={20} color="#FFFFFF" />
          </View>
          
          <View style={styles.fileInfo}>
            <Text variant="body" weight="medium" color="text">
              {item.name}
            </Text>
            <Text variant="caption" color="textSecondary">
              {item.modifiedDate.toLocaleDateString()} · {item.modifiedDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
            </Text>
          </View>
          
          <TouchableOpacity style={styles.menuButton}>
            <Icon name="more-horizontal" size={20} color={theme.colors.textSecondary} />
          </TouchableOpacity>
        </View>
        
        {isMiddleItem && (
          <View style={styles.previewBadge}>
            <View style={[styles.badgeIndicator, { backgroundColor: '#FF8AD6' }]} />
          </View>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Navigation Bar */}
      <FadeInView style={styles.navBar}>
        <TouchableOpacity style={styles.backButton} onPress={onBack}>
          <Icon name="chevron-left" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </FadeInView>

      {/* Folder Header */}
      <FadeInView style={styles.folderHeader} delay={100}>
        <Text variant="body" color="textSecondary">
          My Folders
        </Text>
        <Text variant="heading" color="text" style={styles.folderTitle}>
          Compositions Tutorial
        </Text>
        
        <TouchableOpacity style={styles.addFilesButton}>
          <View style={styles.dashedBorder}>
            <Icon name="upload" size={16} color="rgba(255, 255, 255, 0.6)" />
            <Text variant="body" weight="medium" color="text" style={styles.addFilesText}>
              Add files
            </Text>
          </View>
        </TouchableOpacity>
      </FadeInView>

      {/* Hero Preview */}
      <FadeInView style={styles.heroPreview} delay={200}>
        <View style={[styles.previewContainer, { backgroundColor: theme.colors.surface }]}>
          <Image
            source={{ uri: 'https://via.placeholder.com/280x180/FF8AD6/FFFFFF?text=🎨' }}
            style={styles.previewImage}
            resizeMode="cover"
          />
        </View>
      </FadeInView>

      {/* Files List */}
      <FadeInView style={styles.filesList} delay={300}>
        <FlatList
          data={displayFiles}
          renderItem={({ item, index }) => <FileListItem item={item} index={index} />}
          keyExtractor={(item) => item.path}
          showsVerticalScrollIndicator={false}
          ItemSeparatorComponent={() => (
            <View style={[styles.separator, { backgroundColor: theme.colors.border }]} />
          )}
        />
      </FadeInView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  navBar: {
    paddingHorizontal: 16,
    paddingTop: 52,
    paddingBottom: 16,
  },
  backButton: {
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
  },
  folderHeader: {
    paddingHorizontal: 16,
    paddingBottom: 24,
  },
  folderTitle: {
    marginTop: 4,
    marginBottom: 16,
  },
  addFilesButton: {
    alignSelf: 'flex-start',
  },
  dashedBorder: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 14,
    borderWidth: 1.5,
    borderColor: 'rgba(255, 255, 255, 0.24)',
    borderStyle: 'dashed',
  },
  addFilesText: {
    marginLeft: 6,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  heroPreview: {
    paddingHorizontal: 46,
    paddingBottom: 24,
  },
  previewContainer: {
    borderRadius: 22,
    padding: 18,
    alignItems: 'center',
    justifyContent: 'center',
    height: 220,
  },
  previewImage: {
    width: 280,
    height: 180,
    borderRadius: 16,
  },
  filesList: {
    flex: 1,
    paddingHorizontal: 16,
  },
  fileItem: {
    borderRadius: 14,
    padding: 16,
    position: 'relative',
  },
  fileItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  fileThumbnail: {
    width: 36,
    height: 36,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  fileInfo: {
    flex: 1,
  },
  menuButton: {
    padding: 8,
  },
  previewBadge: {
    position: 'absolute',
    right: 16,
    top: 16,
  },
  badgeIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  separator: {
    height: 1,
    marginLeft: 64,
  },
});
