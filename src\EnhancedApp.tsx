import React, { useEffect } from 'react';
import {
  SafeAreaView,
  StyleSheet,
  BackHandler,
  Alert,
  Platform,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { ThemeProvider, useTheme } from './theme';
import { EnhancedAppNavigator } from './navigation/EnhancedAppNavigator';

const MainApp: React.FC = () => {
  const { theme, isDark } = useTheme();

  // Handle Android back button
  useEffect(() => {
    if (Platform.OS === 'android') {
      const backAction = () => {
        Alert.alert(
          'Exit App',
          'Are you sure you want to exit?',
          [
            {
              text: 'Cancel',
              onPress: () => null,
              style: 'cancel',
            },
            {
              text: 'Exit',
              onPress: () => BackHandler.exitApp(),
            },
          ]
        );
        return true;
      };

      const backHandler = BackHandler.addEventListener(
        'hardwareBackPress',
        backAction
      );

      return () => backHandler.remove();
    }
  }, []);

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <StatusBar style={isDark ? 'light' : 'dark'} backgroundColor="transparent" translucent />
      <EnhancedAppNavigator />
    </SafeAreaView>
  );
};

const EnhancedApp: React.FC = () => {
  return (
    <ThemeProvider>
      <MainApp />
    </ThemeProvider>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default EnhancedApp;
