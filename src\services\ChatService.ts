import AsyncStorage from '@react-native-async-storage/async-storage';
import { ChatMessage, ChatSession } from '../types';
import { LangChainService } from './LangChainService';

export class ChatService {
  private static instance: ChatService;
  private currentSession: ChatSession | null = null;
  private langChainService: LangChainService;

  public static getInstance(): ChatService {
    if (!ChatService.instance) {
      ChatService.instance = new ChatService();
    }
    return ChatService.instance;
  }

  private constructor() {
    this.langChainService = LangChainService.getInstance();
    this.initializeSession();
  }

  /**
   * Send a message and get response
   */
  async sendMessage(message: string): Promise<ChatMessage> {
    try {
      const userMessage: ChatMessage = {
        id: this.generateId(),
        text: message,
        timestamp: new Date(),
        sender: 'user',
        type: 'text',
      };

      // Save user message
      await this.saveMessage(userMessage);

      // Get conversation history for context
      const conversationHistory = await this.getMessageHistory();
      
      // Generate assistant response using LangChain service
      const assistantResponse = await this.langChainService.generateResponse(
        message, 
        conversationHistory.slice(0, -1) // Exclude the message we just added
      );
      
      const assistantMessage: ChatMessage = {
        id: this.generateId(),
        text: assistantResponse,
        timestamp: new Date(),
        sender: 'assistant',
        type: 'text',
      };

      // Save assistant message
      await this.saveMessage(assistantMessage);

      return assistantMessage;
    } catch (error) {
      console.error('Failed to send message:', error);
      // Fallback to basic response if AI fails
      const fallbackResponse = this.generateBasicResponse(message);
      const assistantMessage: ChatMessage = {
        id: this.generateId(),
        text: fallbackResponse,
        timestamp: new Date(),
        sender: 'assistant',
        type: 'text',
      };
      await this.saveMessage(assistantMessage);
      return assistantMessage;
    }
  }

  /**
   * Get message history
   */
  async getMessageHistory(): Promise<ChatMessage[]> {
    try {
      if (!this.currentSession) {
        await this.initializeSession();
      }

      return this.currentSession?.messages || [];
    } catch (error) {
      console.error('Failed to get message history:', error);
      return [];
    }
  }

  /**
   * Save a message
   */
  async saveMessage(message: ChatMessage): Promise<void> {
    try {
      if (!this.currentSession) {
        await this.initializeSession();
      }

      if (this.currentSession) {
        this.currentSession.messages.push(message);
        this.currentSession.lastActivity = new Date();
        await this.saveSession();
      }
    } catch (error) {
      console.error('Failed to save message:', error);
    }
  }

  /**
   * Clear message history
   */
  async clearHistory(): Promise<void> {
    try {
      if (this.currentSession) {
        this.currentSession.messages = [];
        this.currentSession.lastActivity = new Date();
        await this.saveSession();
      }
    } catch (error) {
      console.error('Failed to clear history:', error);
    }
  }

  /**
   * Send a file reference message
   */
  async sendFileReference(filePath: string, action: string): Promise<ChatMessage> {
    try {
      const message: ChatMessage = {
        id: this.generateId(),
        text: `File action: ${action}`,
        timestamp: new Date(),
        sender: 'user',
        type: 'file_reference',
        metadata: {
          fileReference: filePath,
          actionType: action,
        },
      };

      await this.saveMessage(message);

      // Generate contextual response using LangChain service
      const response = await this.langChainService.generateFileActionResponse(filePath, action);
      const assistantMessage: ChatMessage = {
        id: this.generateId(),
        text: response,
        timestamp: new Date(),
        sender: 'assistant',
        type: 'text',
      };

      await this.saveMessage(assistantMessage);

      return assistantMessage;
    } catch (error) {
      console.error('Failed to send file reference:', error);
      throw new Error('Failed to send file reference');
    }
  }

  /**
   * Get chat statistics
   */
  async getChatStats(): Promise<{
    totalMessages: number;
    userMessages: number;
    assistantMessages: number;
    sessionDuration: number;
  }> {
    try {
      const messages = await this.getMessageHistory();
      const userMessages = messages.filter(m => m.sender === 'user').length;
      const assistantMessages = messages.filter(m => m.sender === 'assistant').length;
      
      const sessionDuration = this.currentSession
        ? Date.now() - this.currentSession.createdAt.getTime()
        : 0;

      return {
        totalMessages: messages.length,
        userMessages,
        assistantMessages,
        sessionDuration,
      };
    } catch (error) {
      console.error('Failed to get chat stats:', error);
      return {
        totalMessages: 0,
        userMessages: 0,
        assistantMessages: 0,
        sessionDuration: 0,
      };
    }
  }

  /**
   * Search messages
   */
  async searchMessages(query: string): Promise<ChatMessage[]> {
    try {
      const messages = await this.getMessageHistory();
      const lowercaseQuery = query.toLowerCase();

      return messages.filter(message =>
        message.text.toLowerCase().includes(lowercaseQuery)
      );
    } catch (error) {
      console.error('Failed to search messages:', error);
      return [];
    }
  }

  // Private methods

  private generateBasicResponse(message: string): string {
    const lowercaseMessage = message.toLowerCase();
    
    // File management related responses
    if (lowercaseMessage.includes('search') || lowercaseMessage.includes('find')) {
      return 'I can help you search for files! Use the search icon in the file manager to find files by name, type, or content. You can also filter results by date, size, or file type.';
    }
    
    if (lowercaseMessage.includes('organize') || lowercaseMessage.includes('sort')) {
      return 'Great! I can help you organize your files. You can sort files by name, date, size, or type. You can also create folders to group related files together.';
    }
    
    if (lowercaseMessage.includes('delete') || lowercaseMessage.includes('remove')) {
      return 'To delete files, long-press on any file or folder to open the context menu, then select delete. Be careful as this action cannot be undone!';
    }
    
    if (lowercaseMessage.includes('copy') || lowercaseMessage.includes('move')) {
      return 'You can copy or move files by long-pressing on them to open the context menu. Select copy or move, then navigate to your destination folder and paste.';
    }
    
    if (lowercaseMessage.includes('hello') || lowercaseMessage.includes('hi')) {
      return 'Hello! I\'m here to help you manage your files efficiently. What would you like to do today?';
    }
    
    if (lowercaseMessage.includes('help')) {
      return 'I can help you with:\n• Searching and finding files\n• Organizing and sorting files\n• Creating, copying, and moving files\n• Understanding file types and sizes\n• Managing folders and directories\n\nWhat specific task would you like help with?';
    }
    
    // Default responses
    const defaultResponses = [
      'That\'s interesting! How can I help you with your file management needs?',
      'I understand. Is there anything specific you\'d like to do with your files?',
      'Thanks for sharing! Let me know if you need help organizing or finding any files.',
      'I\'m here to help with your file management tasks. What would you like to do?',
      'That sounds good! Feel free to ask me about searching, organizing, or managing your files.',
    ];
    
    return defaultResponses[Math.floor(Math.random() * defaultResponses.length)];
  }

  private async initializeSession(): Promise<void> {
    try {
      const sessionData = await AsyncStorage.getItem('current_chat_session');
      
      if (sessionData) {
        const session = JSON.parse(sessionData);
        // Convert date strings back to Date objects
        session.createdAt = new Date(session.createdAt);
        session.lastActivity = new Date(session.lastActivity);
        session.messages = session.messages.map((msg: any) => ({
          ...msg,
          timestamp: new Date(msg.timestamp),
        }));
        
        this.currentSession = session;
      } else {
        // Create new session
        this.currentSession = {
          id: this.generateId(),
          messages: [
            {
              id: this.generateId(),
              text: 'Hello! I\'m your file management assistant. I can help you organize, search, and manage your files. How can I assist you today?',
              timestamp: new Date(),
              sender: 'assistant',
              type: 'system',
            },
          ],
          createdAt: new Date(),
          lastActivity: new Date(),
        };
        
        await this.saveSession();
      }
    } catch (error) {
      console.error('Failed to initialize session:', error);
      // Create fallback session
      this.currentSession = {
        id: this.generateId(),
        messages: [],
        createdAt: new Date(),
        lastActivity: new Date(),
      };
    }
  }

  private async saveSession(): Promise<void> {
    try {
      if (this.currentSession) {
        await AsyncStorage.setItem(
          'current_chat_session',
          JSON.stringify(this.currentSession)
        );
      }
    } catch (error) {
      console.error('Failed to save session:', error);
    }
  }





  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }
}