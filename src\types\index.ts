// File System Types
export interface FileItem {
  name: string;
  path: string;
  type: 'file' | 'directory';
  size: number;
  modifiedDate: Date;
  mimeType?: string | undefined;
  thumbnail?: string | undefined;
  isHidden: boolean;
  permissions: FilePermissions;
  mediaAsset?: any; // MediaLibrary.Asset for media files
  isMediaFile?: boolean;
  location?: {
    latitude?: number;
    longitude?: number;
  };
  // Placeholder properties for when media access is not available
  isPlaceholder?: boolean;
  placeholderType?: 'photos' | 'videos' | 'audio';
}

export interface FilePermissions {
  readable: boolean;
  writable: boolean;
  executable: boolean;
}

export interface DirectoryContents {
  path: string;
  items: FileItem[];
  totalSize: number;
  itemCount: number;
}

// Chat Types
export interface ChatMessage {
  id: string;
  text: string;
  timestamp: Date;
  sender: 'user' | 'assistant';
  type: 'text' | 'file_reference' | 'system';
  metadata?: {
    fileReference?: string;
    actionType?: string;
  };
}

export interface ChatSession {
  id: string;
  messages: ChatMessage[];
  createdAt: Date;
  lastActivity: Date;
}

// App State Types
export interface AppState {
  currentPath: string;
  selectedFiles: string[];
  viewMode: 'list' | 'grid' | 'detailed';
  sortBy: 'name' | 'date' | 'size' | 'type';
  sortOrder: 'asc' | 'desc';
  showHiddenFiles: boolean;
  theme: 'light' | 'dark';
  chatVisible: boolean;
}

export interface NavigationState {
  history: string[];
  currentIndex: number;
  canGoBack: boolean;
  canGoForward: boolean;
}

// Error Types
export enum FileManagerError {
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  FILE_NOT_FOUND = 'FILE_NOT_FOUND',
  STORAGE_FULL = 'STORAGE_FULL',
  NETWORK_ERROR = 'NETWORK_ERROR',
  INVALID_OPERATION = 'INVALID_OPERATION'
}

export interface ErrorState {
  type: FileManagerError;
  message: string;
  recoverable: boolean;
  retryAction?: () => void;
}

// Component Props Types
export interface FileManagerScreenProps {
  initialPath?: string;
  onFileSelect?: (file: FileItem) => void;
}

export interface ChatInterfaceProps {
  isVisible: boolean;
  onClose: () => void;
  messages: ChatMessage[];
  onSendMessage: (message: string) => void;
}

export interface FileListItemProps {
  item: FileItem;
  viewMode: 'list' | 'grid' | 'detailed';
  onPress: () => void;
  onLongPress: () => void;
  isSelected: boolean;
}