import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  StatusBar,
  Dimensions,
} from 'react-native';
import { useTheme } from '../theme';
import { Text } from '../components/Text';
import { 
  Card, 
  IconButton, 
  Chip, 
  ListItem 
} from '../components/design-system';
import { Icon, IconNames } from '../components/Icon';
import { DeviceFileSystemService } from '../services/DeviceFileSystemService';
import { FileItem } from '../types';

const { width: screenWidth } = Dimensions.get('window');

interface EnhancedFolderScreenProps {
  onNavigateBack?: () => void;
  folderPath?: string;
  folderName?: string;
}

export const EnhancedFolderScreen: React.FC<EnhancedFolderScreenProps> = ({
  onNavigateBack,
  folderPath = '/compositions-tutorial',
  folderName = 'Compositions Tutorial',
}) => {
  const { theme } = useTheme();
  const [files, setFiles] = useState<FileItem[]>([]);
  const [selectedFiles, setSelectedFiles] = useState<Set<string>>(new Set());

  useEffect(() => {
    loadFiles();
  }, [folderPath]);

  const loadFiles = async () => {
    try {
      const deviceService = DeviceFileSystemService.getInstance();
      const folderFiles = await deviceService.listFiles(folderPath);
      setFiles(folderFiles);
    } catch (error) {
      console.error('Failed to load folder files:', error);
      // Demo data for the tutorial folder
      setFiles([
        {
          name: 'toy_cluster.png',
          path: `${folderPath}/toy_cluster.png`,
          type: 'file',
          size: 2048000,
          modifiedDate: new Date('2023-08-12T15:38:00'),
          mimeType: 'image/png',
          isHidden: false,
          permissions: { readable: true, writable: true, executable: false },
        },
        {
          name: 'composition_guide.pdf',
          path: `${folderPath}/composition_guide.pdf`,
          type: 'file',
          size: 1024000,
          modifiedDate: new Date('2023-08-12T14:22:00'),
          mimeType: 'application/pdf',
          isHidden: false,
          permissions: { readable: true, writable: true, executable: false },
        },
        {
          name: 'tutorial_video.mp4',
          path: `${folderPath}/tutorial_video.mp4`,
          type: 'file',
          size: 15728640,
          modifiedDate: new Date('2023-08-12T13:15:00'),
          mimeType: 'video/mp4',
          isHidden: false,
          permissions: { readable: true, writable: true, executable: false },
        },
      ]);
    }
  };

  const handleFilePress = (file: FileItem) => {
    const newSelected = new Set(selectedFiles);
    if (newSelected.has(file.path)) {
      newSelected.delete(file.path);
    } else {
      newSelected.add(file.path);
    }
    setSelectedFiles(newSelected);
  };

  const handleAddFiles = () => {
    // Handle add files action
    console.log('Add files to folder');
  };

  const getFileIcon = (file: FileItem): IconNames => {
    if (file.mimeType?.startsWith('image/')) return IconNames.IMAGE;
    if (file.mimeType?.startsWith('video/')) return IconNames.VIDEO;
    if (file.mimeType?.includes('pdf')) return IconNames.FILE_TEXT;
    return IconNames.FILE;
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const formatDate = (date: Date): string => {
    return date.toLocaleDateString('en-US', {
      month: '2-digit',
      day: '2-digit',
      year: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      {/* Transparent Header */}
      <View style={styles.header}>
        <IconButton
          icon={IconNames.BACK}
          onPress={onNavigateBack}
          variant="ghost"
          style={styles.backButton}
        />
      </View>

      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.folderHeader}>
          <Text variant="body" color="textTertiary" style={styles.breadcrumb}>
            My Folders
          </Text>
          <Text style={[
            theme.typography.display.medium,
            { color: theme.colors.text }
          ]}>
            {folderName}
          </Text>
          
          <Chip
            label="Add files"
            icon={IconNames.PLUS}
            onPress={handleAddFiles}
            variant="outline"
            style={[styles.addFilesButton, { 
              borderStyle: 'dashed',
              borderWidth: 1.5,
              borderColor: theme.colors.border,
            }]}
          />
        </View>

        {/* Hero Preview */}
        <Card style={[styles.heroCard, { backgroundColor: theme.colors.surfaceAlt }]} elevation="x1">
          <View style={styles.heroContent}>
            <View style={[styles.placeholderImage, { backgroundColor: theme.colors.border }]}>
              <Text variant="caption" color="textSecondary">
                Preview Image
              </Text>
            </View>
          </View>
        </Card>

        {/* Files List */}
        <View style={styles.filesSection}>
          <Card style={styles.filesCard} elevation="x1">
            {files.map((file, index) => (
              <ListItem
                key={file.path}
                title={file.name}
                subtitle={`${formatDate(file.modifiedDate)} · ${formatFileSize(file.size)}`}
                leftIcon={getFileIcon(file)}
                rightIcon={IconNames.MORE}
                selected={selectedFiles.has(file.path)}
                onPress={() => handleFilePress(file)}
                showDivider={index < files.length - 1}
              />
            ))}
          </Card>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    position: 'absolute',
    top: 60,
    left: 20,
    zIndex: 10,
  },
  backButton: {
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingTop: 100,
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
  folderHeader: {
    marginBottom: 32,
  },
  breadcrumb: {
    marginBottom: 8,
  },
  addFilesButton: {
    marginTop: 20,
    alignSelf: 'flex-start',
  },
  heroCard: {
    borderRadius: 22,
    padding: 18,
    marginBottom: 32,
    minHeight: 220,
  },
  heroContent: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  placeholderImage: {
    width: 298 - 36, // Card width minus padding
    height: 220 - 36, // Card height minus padding
    maxWidth: '100%',
    maxHeight: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 12,
  },
  filesSection: {
    marginBottom: 32,
  },
  filesCard: {
    padding: 0,
    overflow: 'hidden',
  },
});
