import React, { useEffect } from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableWithoutFeedback,
  ViewStyle,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import { useTheme, createShadow } from '../theme';

interface SlideUpPanelProps {
  visible: boolean;
  onClose: () => void;
  children: React.ReactNode;
  height?: number | string;
  style?: ViewStyle;
  backdropOpacity?: number;
  closeOnBackdropPress?: boolean;
}

const { height: screenHeight } = Dimensions.get('window');

export const SlideUpPanel: React.FC<SlideUpPanelProps> = ({
  visible,
  onClose,
  children,
  height = '50%',
  style,
  backdropOpacity = 0.5,
  closeOnBackdropPress = true,
}) => {
  const { theme, isDark } = useTheme();
  const translateY = useSharedValue(screenHeight);
  const opacity = useSharedValue(0);

  const panelHeight = typeof height === 'string' 
    ? (screenHeight * parseFloat(height.replace('%', '')) / 100)
    : height;

  useEffect(() => {
    if (visible) {
      opacity.value = withTiming(1, { duration: 300 });
      translateY.value = withSpring(screenHeight - panelHeight, {
        damping: 20,
        stiffness: 300,
      });
    } else {
      opacity.value = withTiming(0, { duration: 200 });
      translateY.value = withSpring(screenHeight, {
        damping: 20,
        stiffness: 300,
      });
    }
  }, [visible, panelHeight, opacity, translateY]);

  const backdropStyle = useAnimatedStyle(() => ({
    opacity: opacity.value * backdropOpacity,
  }));

  const panelStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: translateY.value }],
  }));

  const handleBackdropPress = () => {
    if (closeOnBackdropPress) {
      onClose();
    }
  };

  if (!visible) {
    return null;
  }

  return (
    <View style={styles.container}>
      <TouchableWithoutFeedback onPress={handleBackdropPress}>
        <Animated.View style={[styles.backdrop, backdropStyle]} />
      </TouchableWithoutFeedback>
      
      <Animated.View
        style={[
          styles.panel,
          {
            height: panelHeight,
            backgroundColor: theme.colors.background,
            borderTopLeftRadius: theme.radii.xl,
            borderTopRightRadius: theme.radii.xl,
            ...createShadow(8, isDark),
          },
          panelStyle,
          style,
        ]}>
        <View style={[styles.handle, { backgroundColor: theme.colors.border }]} />
        <View style={styles.content}>
          {children}
        </View>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
    zIndex: 1000,
  },
  backdrop: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: '#000000',
  },
  panel: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
  },
  handle: {
    width: 40,
    height: 4,
    borderRadius: 2,
    alignSelf: 'center',
    marginTop: 8,
    marginBottom: 16,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
});