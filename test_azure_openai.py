#!/usr/bin/env python3
"""
Test Azure OpenAI configuration to verify the deployment name and API key work correctly.
"""

import os
import requests
import json

# Azure OpenAI Configuration
AZURE_OPENAI_API_KEY = "84a0tBbHjYxc2hjaVeJehwaQckrlu1BwWf2Lvf77lYTghrWQvAXQJQQJ99BGAC77bzfXJ3w3AAABACOGeuWT"
AZURE_OPENAI_ENDPOINT = "https://sivanithish-test.openai.azure.com/"
AZURE_OPENAI_API_VERSION = "2024-12-01-preview"
AZURE_OPENAI_DEPLOYMENT = "gpt-4.1"

def test_azure_openai():
    """Test Azure OpenAI API connection and deployment."""
    
    # Construct the API URL
    url = f"{AZURE_OPENAI_ENDPOINT}openai/deployments/{AZURE_OPENAI_DEPLOYMENT}/chat/completions?api-version={AZURE_OPENAI_API_VERSION}"
    
    headers = {
        "Content-Type": "application/json",
        "api-key": AZURE_OPENAI_API_KEY
    }
    
    # Test payload
    payload = {
        "messages": [
            {
                "role": "system",
                "content": "You are a helpful assistant."
            },
            {
                "role": "user", 
                "content": "Hello! This is a test message."
            }
        ],
        "max_tokens": 50,
        "temperature": 0.1
    }
    
    print("Testing Azure OpenAI Configuration...")
    print(f"Endpoint: {AZURE_OPENAI_ENDPOINT}")
    print(f"Deployment: {AZURE_OPENAI_DEPLOYMENT}")
    print(f"API Version: {AZURE_OPENAI_API_VERSION}")
    print(f"URL: {url}")
    print("-" * 50)
    
    try:
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ SUCCESS! Azure OpenAI is working correctly.")
            print(f"Response: {result['choices'][0]['message']['content']}")
            return True
        else:
            print("❌ FAILED! Azure OpenAI request failed.")
            print(f"Error: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ FAILED! Network error: {e}")
        return False
    except Exception as e:
        print(f"❌ FAILED! Unexpected error: {e}")
        return False

def test_deployments_list():
    """List available deployments to verify the deployment name."""
    
    url = f"{AZURE_OPENAI_ENDPOINT}openai/deployments?api-version={AZURE_OPENAI_API_VERSION}"
    
    headers = {
        "api-key": AZURE_OPENAI_API_KEY
    }
    
    print("\nListing available deployments...")
    print("-" * 50)
    
    try:
        response = requests.get(url, headers=headers, timeout=30)
        
        if response.status_code == 200:
            deployments = response.json()
            print("✅ Available deployments:")
            for deployment in deployments.get('data', []):
                print(f"  - {deployment.get('id', 'Unknown')} (Model: {deployment.get('model', 'Unknown')})")
            return deployments
        else:
            print(f"❌ Failed to list deployments: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error listing deployments: {e}")
        return None

if __name__ == "__main__":
    print("🔧 Azure OpenAI Configuration Test")
    print("=" * 50)
    
    # Test the configuration
    success = test_azure_openai()
    
    # List available deployments for debugging
    deployments = test_deployments_list()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ Configuration is CORRECT! You can use these settings in your app.")
    else:
        print("❌ Configuration has ISSUES. Check the deployment name and API key.")
        if deployments:
            print("\n💡 Try using one of the available deployment names listed above.")
