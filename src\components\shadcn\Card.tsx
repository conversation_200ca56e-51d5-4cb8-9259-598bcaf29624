/**
 * Card Component - shadcn/ui inspired
 * Clean, modern card with subtle animations and shadows
 */

import React from 'react';
import { View, ViewStyle, TouchableOpacity } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  interpolate,
} from 'react-native-reanimated';
import { useTheme } from '../../theme';
import { SPRING_CONFIG, SCALE_VALUES } from '../../animation/AnimationConfig';
import AnimationManager from '../../animation/AnimationManager';

const AnimatedView = Animated.createAnimatedComponent(View);
const AnimatedTouchable = Animated.createAnimatedComponent(TouchableOpacity);

interface CardProps {
  children: React.ReactNode;
  style?: ViewStyle;
  onPress?: () => void;
  pressable?: boolean;
  variant?: 'default' | 'outline' | 'ghost';
  padding?: 'none' | 'sm' | 'md' | 'lg';
  shadow?: boolean;
}

export const Card: React.FC<CardProps> = ({
  children,
  style,
  onPress,
  pressable = false,
  variant = 'default',
  padding = 'md',
  shadow = true,
}) => {
  const { theme, isDark } = useTheme();
  const scale = useSharedValue(1);
  const pressed = useSharedValue(0);
  const animationManager = AnimationManager.getInstance();

  const handlePressIn = () => {
    if (!pressable) return;

    const scaleAnimationId = `card-scale-${Date.now()}`;
    const pressAnimationId = `card-press-${Date.now()}`;

    scale.value = withSpring(SCALE_VALUES.subtle, SPRING_CONFIG.gentle);
    pressed.value = withSpring(1, SPRING_CONFIG.gentle);

    animationManager.registerAnimation(scaleAnimationId, scale, 'medium', 'spring');
    animationManager.registerAnimation(pressAnimationId, pressed, 'medium', 'spring');
  };

  const handlePressOut = () => {
    if (!pressable) return;

    const scaleAnimationId = `card-scale-release-${Date.now()}`;
    const pressAnimationId = `card-press-release-${Date.now()}`;

    scale.value = withSpring(1, SPRING_CONFIG.gentle);
    pressed.value = withSpring(0, SPRING_CONFIG.gentle);

    animationManager.registerAnimation(scaleAnimationId, scale, 'medium', 'spring');
    animationManager.registerAnimation(pressAnimationId, pressed, 'medium', 'spring');
  };

  const animatedStyle = useAnimatedStyle(() => {
    const shadowOpacity = interpolate(
      pressed.value,
      [0, 1],
      [isDark ? 0.3 : 0.1, isDark ? 0.4 : 0.15]
    );

    const shadowRadius = interpolate(
      pressed.value,
      [0, 1],
      [4, 8]
    );

    const elevation = interpolate(
      pressed.value,
      [0, 1],
      [2, 4]
    );

    return {
      transform: [{ scale: scale.value }],
      ...(shadow && {
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity,
        shadowRadius,
        elevation,
      }),
    };
  });

  // Get variant styles
  const getVariantStyles = (): ViewStyle => {
    switch (variant) {
      case 'default':
        return {
          backgroundColor: theme.colors.card,
          borderColor: theme.colors.border,
          borderWidth: 1,
        };
      case 'outline':
        return {
          backgroundColor: 'transparent',
          borderColor: theme.colors.border,
          borderWidth: 1,
        };
      case 'ghost':
        return {
          backgroundColor: 'transparent',
          borderColor: 'transparent',
          borderWidth: 0,
        };
      default:
        return {
          backgroundColor: theme.colors.card,
          borderColor: theme.colors.border,
          borderWidth: 1,
        };
    }
  };

  // Get padding styles
  const getPaddingStyles = (): ViewStyle => {
    switch (padding) {
      case 'none':
        return {};
      case 'sm':
        return {
          padding: 12,
        };
      case 'md':
        return {
          padding: 16,
        };
      case 'lg':
        return {
          padding: 24,
        };
      default:
        return {
          padding: 16,
        };
    }
  };

  const cardStyle: ViewStyle = {
    ...getVariantStyles(),
    ...getPaddingStyles(),
    borderRadius: theme.radii.lg,
    ...style,
  };

  if (pressable && onPress) {
    return (
      <AnimatedTouchable
        style={[animatedStyle, cardStyle]}
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={1}
      >
        {children}
      </AnimatedTouchable>
    );
  }

  return (
    <AnimatedView style={[animatedStyle, cardStyle]}>
      {children}
    </AnimatedView>
  );
};

// Card sub-components for better composition
export const CardHeader: React.FC<{
  children: React.ReactNode;
  style?: ViewStyle;
}> = ({ children, style }) => {
  return (
    <View style={[{ marginBottom: 12 }, style]}>
      {children}
    </View>
  );
};

export const CardContent: React.FC<{
  children: React.ReactNode;
  style?: ViewStyle;
}> = ({ children, style }) => {
  return (
    <View style={[{ flex: 1 }, style]}>
      {children}
    </View>
  );
};

export const CardFooter: React.FC<{
  children: React.ReactNode;
  style?: ViewStyle;
}> = ({ children, style }) => {
  return (
    <View style={[{ marginTop: 12, flexDirection: 'row', alignItems: 'center' }, style]}>
      {children}
    </View>
  );
};
