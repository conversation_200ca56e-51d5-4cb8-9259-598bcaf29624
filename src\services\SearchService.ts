import AsyncStorage from '@react-native-async-storage/async-storage';
import { FileItem } from '../types';
import { FileSystemService } from './FileSystemService';
import { FileTypeService } from './FileTypeService';
import { DeviceFileSystemService } from './DeviceFileSystemService';

interface SearchIndex {
  [path: string]: {
    files: FileItem[];
    lastIndexed: Date;
  };
}

interface SearchResult extends FileItem {
  relevanceScore: number;
  matchedText: string;
}

export class SearchService {
  private static instance: SearchService;
  private fileSystemService: FileSystemService;
  private fileTypeService: FileTypeService;
  private deviceFileSystemService: DeviceFileSystemService;
  private searchIndex: SearchIndex = {};
  private recentSearches: string[] = [];
  private searchHistory: string[] = [];

  public static getInstance(): SearchService {
    if (!SearchService.instance) {
      SearchService.instance = new SearchService();
    }
    return SearchService.instance;
  }

  private constructor() {
    this.fileSystemService = FileSystemService.getInstance();
    this.fileTypeService = FileTypeService.getInstance();
    this.deviceFileSystemService = DeviceFileSystemService.getInstance();
    this.loadSearchHistory();
  }

  /**
   * Index directory for faster search
   */
  async indexDirectory(path: string): Promise<void> {
    try {
      const files = path.startsWith('media://')
        ? await this.deviceFileSystemService.listDirectory(path)
        : await this.fileSystemService.listDirectory(path);
      
      this.searchIndex[path] = {
        files,
        lastIndexed: new Date(),
      };

      // Index subdirectories recursively (limit depth to avoid performance issues)
      const directories = files.filter(file => file.type === 'directory');
      for (const dir of directories.slice(0, 10)) { // Limit to 10 subdirectories
        try {
          await this.indexDirectory(dir.path);
        } catch (error) {
          console.warn(`Failed to index directory: ${dir.path}`, error);
        }
      }
    } catch (error) {
      console.error(`Failed to index directory: ${path}`, error);
    }
  }

  /**
   * Search files with debounced input
   */
  async searchFiles(query: string, path?: string): Promise<SearchResult[]> {
    if (!query.trim()) {
      return [];
    }

    // Add to search history
    await this.addToSearchHistory(query);

    const searchResults: SearchResult[] = [];
    const lowercaseQuery = query.toLowerCase();

    // Search in specific path or all indexed paths
    const pathsToSearch = path ? [path] : Object.keys(this.searchIndex);

    for (const searchPath of pathsToSearch) {
      const indexData = this.searchIndex[searchPath];
      if (!indexData) {
        // If path not indexed, try to index it first
        if (path === searchPath) {
          await this.indexDirectory(searchPath);
          const newIndexData = this.searchIndex[searchPath];
          if (newIndexData) {
            const results = this.searchInFiles(newIndexData.files, lowercaseQuery);
            searchResults.push(...results);
          }
        }
        continue;
      }

      // Check if index is stale (older than 5 minutes)
      const isStale = Date.now() - indexData.lastIndexed.getTime() > 5 * 60 * 1000;
      if (isStale) {
        await this.indexDirectory(searchPath);
        const updatedIndexData = this.searchIndex[searchPath];
        if (updatedIndexData) {
          const results = this.searchInFiles(updatedIndexData.files, lowercaseQuery);
          searchResults.push(...results);
        }
      } else {
        const results = this.searchInFiles(indexData.files, lowercaseQuery);
        searchResults.push(...results);
      }
    }

    // Sort by relevance score
    return searchResults
      .sort((a, b) => b.relevanceScore - a.relevanceScore)
      .slice(0, 50); // Limit results
  }

  /**
   * Get recent files
   */
  async getRecentFiles(limit: number = 10): Promise<FileItem[]> {
    try {
      const recentFilesData = await AsyncStorage.getItem('recent_files');
      if (!recentFilesData) {
        return [];
      }

      const recentFiles: FileItem[] = JSON.parse(recentFilesData);
      return recentFiles.slice(0, limit);
    } catch (error) {
      console.error('Failed to get recent files:', error);
      return [];
    }
  }

  /**
   * Add file to recent files
   */
  async addToRecentFiles(file: FileItem): Promise<void> {
    try {
      const recentFiles = await this.getRecentFiles(20);
      
      // Remove if already exists
      const filteredFiles = recentFiles.filter(f => f.path !== file.path);
      
      // Add to beginning
      const updatedFiles = [file, ...filteredFiles].slice(0, 20);
      
      await AsyncStorage.setItem('recent_files', JSON.stringify(updatedFiles));
    } catch (error) {
      console.error('Failed to add to recent files:', error);
    }
  }

  /**
   * Get search suggestions
   */
  async getSearchSuggestions(query: string): Promise<string[]> {
    if (!query.trim()) {
      return this.recentSearches.slice(0, 5);
    }

    const lowercaseQuery = query.toLowerCase();
    const suggestions = this.searchHistory
      .filter(search => search.toLowerCase().includes(lowercaseQuery))
      .slice(0, 5);

    return suggestions;
  }

  /**
   * Get search history
   */
  getSearchHistory(): string[] {
    return [...this.searchHistory];
  }

  /**
   * Clear search history
   */
  async clearSearchHistory(): Promise<void> {
    this.searchHistory = [];
    this.recentSearches = [];
    await AsyncStorage.removeItem('search_history');
  }

  /**
   * Search with filters
   */
  async searchWithFilters(
    query: string,
    filters: {
      fileType?: string;
      dateRange?: { start: Date; end: Date };
      sizeRange?: { min: number; max: number };
      path?: string;
    }
  ): Promise<SearchResult[]> {
    let results = await this.searchFiles(query, filters.path);

    // Apply file type filter
    if (filters.fileType) {
      results = results.filter(file => {
        if (filters.fileType === 'folder') {
          return file.type === 'directory';
        }
        const category = this.fileTypeService.getFileCategory(file.name);
        return category === filters.fileType;
      });
    }

    // Apply date range filter
    if (filters.dateRange) {
      results = results.filter(file => {
        const fileDate = file.modifiedDate;
        return fileDate >= filters.dateRange!.start && fileDate <= filters.dateRange!.end;
      });
    }

    // Apply size range filter
    if (filters.sizeRange) {
      results = results.filter(file => {
        return file.size >= filters.sizeRange!.min && file.size <= filters.sizeRange!.max;
      });
    }

    return results;
  }

  // Private methods

  private searchInFiles(files: FileItem[], query: string): SearchResult[] {
    const results: SearchResult[] = [];

    for (const file of files) {
      const relevanceScore = this.calculateRelevanceScore(file, query);
      if (relevanceScore > 0) {
        results.push({
          ...file,
          relevanceScore,
          matchedText: this.getMatchedText(file.name, query),
        });
      }
    }

    return results;
  }

  private calculateRelevanceScore(file: FileItem, query: string): number {
    const fileName = file.name.toLowerCase();
    const queryLower = query.toLowerCase();

    let score = 0;

    // Exact match gets highest score
    if (fileName === queryLower) {
      score += 100;
    }

    // Starts with query gets high score
    if (fileName.startsWith(queryLower)) {
      score += 80;
    }

    // Contains query gets medium score
    if (fileName.includes(queryLower)) {
      score += 50;
    }

    // Word boundary matches get bonus
    const words = fileName.split(/[\s\-_\.]/);
    for (const word of words) {
      if (word.startsWith(queryLower)) {
        score += 30;
      }
      if (word === queryLower) {
        score += 40;
      }
    }

    // File type bonus (prefer certain types)
    if (file.type === 'directory') {
      score += 10;
    }

    // Recent files get bonus
    const daysSinceModified = (Date.now() - file.modifiedDate.getTime()) / (1000 * 60 * 60 * 24);
    if (daysSinceModified < 7) {
      score += 20;
    } else if (daysSinceModified < 30) {
      score += 10;
    }

    return score;
  }

  private getMatchedText(text: string, query: string): string {
    const index = text.toLowerCase().indexOf(query.toLowerCase());
    if (index === -1) {
      return text;
    }

    const start = Math.max(0, index - 10);
    const end = Math.min(text.length, index + query.length + 10);
    
    let result = text.substring(start, end);
    if (start > 0) result = '...' + result;
    if (end < text.length) result = result + '...';

    return result;
  }

  private async addToSearchHistory(query: string): Promise<void> {
    // Remove if already exists
    this.searchHistory = this.searchHistory.filter(search => search !== query);
    
    // Add to beginning
    this.searchHistory.unshift(query);
    
    // Keep only last 50 searches
    this.searchHistory = this.searchHistory.slice(0, 50);
    
    // Update recent searches (last 10)
    this.recentSearches = this.searchHistory.slice(0, 10);

    // Save to storage
    try {
      await AsyncStorage.setItem('search_history', JSON.stringify(this.searchHistory));
    } catch (error) {
      console.error('Failed to save search history:', error);
    }
  }

  private async loadSearchHistory(): Promise<void> {
    try {
      const historyData = await AsyncStorage.getItem('search_history');
      if (historyData) {
        this.searchHistory = JSON.parse(historyData);
        this.recentSearches = this.searchHistory.slice(0, 10);
      }
    } catch (error) {
      console.error('Failed to load search history:', error);
    }
  }
}