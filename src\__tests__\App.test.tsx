import React from 'react';
import { render } from '@testing-library/react-native';
import App from '../App';

// Mock expo modules
jest.mock('expo-status-bar', () => ({
  StatusBar: () => null,
}));

jest.mock('expo-file-system', () => ({
  documentDirectory: '/mock/documents/',
  readDirectoryAsync: jest.fn(),
  getInfoAsync: jest.fn(),
}));

describe('App', () => {
  it('renders without crashing', () => {
    const { getByText } = render(<App />);
    // This is a basic smoke test
    expect(getByText).toBeDefined();
  });
});