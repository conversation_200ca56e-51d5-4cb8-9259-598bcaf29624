import React from 'react';
import { View, Image, ViewStyle, StyleSheet } from 'react-native';
import { useTheme } from '../../theme';
import { Text } from '../Text';

interface AvatarProps {
  source?: { uri: string } | number;
  name?: string;
  size?: 'small' | 'medium' | 'large';
  style?: ViewStyle;
  borderColor?: string;
  borderWidth?: number;
}

export const Avatar: React.FC<AvatarProps> = ({
  source,
  name,
  size = 'medium',
  style,
  borderColor,
  borderWidth = 2,
}) => {
  const { theme } = useTheme();

  const getSizeConfig = () => {
    switch (size) {
      case 'small':
        return { size: 28, fontSize: 12 };
      case 'large':
        return { size: 48, fontSize: 18 };
      default:
        return { size: 36, fontSize: 14 };
    }
  };

  const getInitials = (name: string): string => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const { size: avatarSize, fontSize } = getSizeConfig();
  
  const avatarStyle: ViewStyle = {
    width: avatarSize,
    height: avatarSize,
    borderRadius: avatarSize / 2,
    borderWidth: borderColor ? borderWidth : 0,
    borderColor: borderColor || (theme.mode === 'dark' ? '#12161C' : '#FFFFFF'),
    backgroundColor: theme.colors.surface,
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden',
  };

  return (
    <View style={[avatarStyle, style]}>
      {source ? (
        <Image 
          source={source} 
          style={styles.image}
          resizeMode="cover"
        />
      ) : name ? (
        <Text 
          style={[
            { fontSize, color: theme.colors.textSecondary },
            styles.initials
          ]}
          weight="medium"
        >
          {getInitials(name)}
        </Text>
      ) : (
        <View style={[styles.placeholder, { backgroundColor: theme.colors.backgroundAlt }]} />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  image: {
    width: '100%',
    height: '100%',
  },
  initials: {
    textAlign: 'center',
  },
  placeholder: {
    width: '60%',
    height: '60%',
    borderRadius: 999,
  },
});
