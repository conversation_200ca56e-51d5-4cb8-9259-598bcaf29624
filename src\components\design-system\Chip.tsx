import React from 'react';
import { TouchableOpacity, View, ViewStyle, StyleSheet } from 'react-native';
import { useTheme } from '../../theme';
import { Text } from '../Text';
import { Icon, IconNames } from '../Icon';

interface ChipProps {
  label: string;
  icon?: IconNames;
  onPress?: () => void;
  variant?: 'default' | 'primary' | 'secondary' | 'outline';
  size?: 'small' | 'medium';
  selected?: boolean;
  disabled?: boolean;
  style?: ViewStyle;
}

export const Chip: React.FC<ChipProps> = ({
  label,
  icon,
  onPress,
  variant = 'default',
  size = 'medium',
  selected = false,
  disabled = false,
  style,
}) => {
  const { theme } = useTheme();

  const getSizeConfig = () => {
    switch (size) {
      case 'small':
        return { height: 28, paddingHorizontal: 10, fontSize: 12 };
      default:
        return { height: 32, paddingHorizontal: 12, fontSize: 14 };
    }
  };

  const getChipStyle = (): ViewStyle => {
    const { height, paddingHorizontal } = getSizeConfig();
    const baseStyle: ViewStyle = {
      height,
      paddingHorizontal,
      borderRadius: 14,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    };

    if (selected) {
      return {
        ...baseStyle,
        backgroundColor: theme.colors.primary,
      };
    }

    switch (variant) {
      case 'primary':
        return {
          ...baseStyle,
          backgroundColor: theme.colors.primary,
        };
      case 'secondary':
        return {
          ...baseStyle,
          backgroundColor: theme.colors.surface,
        };
      case 'outline':
        return {
          ...baseStyle,
          backgroundColor: 'transparent',
          borderWidth: 1,
          borderColor: theme.colors.border,
        };
      default:
        return {
          ...baseStyle,
          backgroundColor: theme.colors.backgroundAlt,
        };
    }
  };

  const getTextColor = () => {
    if (disabled) return theme.colors.textTertiary;
    if (selected || variant === 'primary') {
      return theme.mode === 'dark' ? theme.colors.text : '#FFFFFF';
    }
    return theme.colors.text;
  };

  const getIconColor = () => {
    if (disabled) return theme.colors.textTertiary;
    if (selected || variant === 'primary') {
      return theme.mode === 'dark' ? theme.colors.text : '#FFFFFF';
    }
    return theme.colors.textSecondary;
  };

  const { fontSize } = getSizeConfig();

  const content = (
    <View style={[getChipStyle(), disabled && styles.disabled, style]}>
      {icon && (
        <Icon 
          name={icon} 
          size={16} 
          color={getIconColor()} 
          style={styles.icon}
        />
      )}
      <Text 
        style={[
          { fontSize, color: getTextColor() },
          styles.label
        ]}
        weight="medium"
      >
        {label}
      </Text>
    </View>
  );

  if (onPress && !disabled) {
    return (
      <TouchableOpacity onPress={onPress} activeOpacity={0.7}>
        {content}
      </TouchableOpacity>
    );
  }

  return content;
};

const styles = StyleSheet.create({
  disabled: {
    opacity: 0.5,
  },
  icon: {
    marginRight: 6,
  },
  label: {
    textAlign: 'center',
  },
});
