import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  Image,
  ScrollView,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { Video, ResizeMode } from 'expo-av';
import * as FileSystem from 'expo-file-system';
import { FileItem } from '../types';
import { useTheme } from '../theme';
import { Text } from './Text';
import { Button } from './Button';
import { Icon, IconNames } from './Icon';
import { ScaleOnPress } from './ScaleOnPress';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface FilePreviewProps {
  file: FileItem;
  onClose: () => void;
  onShare?: () => void;
  onDelete?: () => void;
}

export const FilePreview: React.FC<FilePreviewProps> = ({
  file,
  onClose,
  onShare,
  onDelete,
}) => {
  const { theme } = useTheme();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [fileContent, setFileContent] = useState<string | null>(null);

  useEffect(() => {
    loadFileContent();
  }, [file]);

  const loadFileContent = async () => {
    try {
      setLoading(true);
      setError(null);

      // For text files, load content
      if (file.mimeType?.startsWith('text/') || 
          file.name.endsWith('.json') || 
          file.name.endsWith('.md')) {
        const content = await FileSystem.readAsStringAsync(file.path);
        setFileContent(content);
      }
    } catch (err) {
      setError('Failed to load file content');
      console.error('File preview error:', err);
    } finally {
      setLoading(false);
    }
  };

  const renderImagePreview = () => (
    <ScrollView
      style={styles.imageContainer}
      contentContainerStyle={styles.imageContentContainer}
      maximumZoomScale={3}
      minimumZoomScale={1}
      showsVerticalScrollIndicator={false}
      showsHorizontalScrollIndicator={false}>
      <Image
        source={{ uri: file.path }}
        style={styles.image}
        resizeMode="contain"
        onLoad={() => setLoading(false)}
        onError={() => {
          setError('Failed to load image');
          setLoading(false);
        }}
      />
    </ScrollView>
  );

  const renderVideoPreview = () => (
    <View style={styles.videoContainer}>
      <Video
        source={{ uri: file.path }}
        style={styles.video}
        useNativeControls
        resizeMode={ResizeMode.CONTAIN}
        isLooping={false}
        onLoad={() => setLoading(false)}
        onError={() => {
          setError('Failed to load video');
          setLoading(false);
        }}
      />
    </View>
  );

  const renderTextPreview = () => (
    <ScrollView style={styles.textContainer} showsVerticalScrollIndicator={false}>
      <Text variant="caption" color="text" style={styles.textContent}>
        {fileContent}
      </Text>
    </ScrollView>
  );

  const renderUnsupportedPreview = () => (
    <View style={styles.unsupportedContainer}>
      <Icon name="file" size={64} color={theme.colors.textSecondary} />
      <Text variant="body" color="textSecondary" style={styles.unsupportedText}>
        Preview not available for this file type
      </Text>
      <Text variant="caption" color="textSecondary" style={styles.fileTypeText}>
        {file.mimeType || 'Unknown file type'}
      </Text>
    </View>
  );

  const renderContent = () => {
    if (loading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text variant="body" color="textSecondary" style={styles.loadingText}>
            Loading preview...
          </Text>
        </View>
      );
    }

    if (error) {
      return (
        <View style={styles.errorContainer}>
          <Icon name="alert-circle" size={48} color={theme.colors.error} />
          <Text variant="body" color="error" style={styles.errorText}>
            {error}
          </Text>
          <Button
            title="Retry"
            onPress={loadFileContent}
            variant="outline"
            style={styles.retryButton}
          />
        </View>
      );
    }

    // Determine preview type based on MIME type
    if (file.mimeType?.startsWith('image/')) {
      return renderImagePreview();
    } else if (file.mimeType?.startsWith('video/')) {
      return renderVideoPreview();
    } else if (file.mimeType?.startsWith('text/') || 
               file.name.endsWith('.json') || 
               file.name.endsWith('.md')) {
      return renderTextPreview();
    } else {
      return renderUnsupportedPreview();
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={[styles.previewHeader, { backgroundColor: theme.colors.surface }]}>
        <View style={styles.headerRow}>
          <ScaleOnPress onPress={onClose}>
            <View style={[styles.headerButton, { backgroundColor: theme.colors.border }]}>
              <Icon name="x" size={20} color={theme.colors.text} />
            </View>
          </ScaleOnPress>
          
          <View style={styles.fileInfo}>
            <Text variant="body" color="text" weight="medium" numberOfLines={1}>
              {file.name}
            </Text>
            <Text variant="caption" color="textSecondary">
              {formatFileSize(file.size)} • {file.modifiedDate.toLocaleDateString()}
            </Text>
          </View>

          <View style={styles.headerActions}>
            {onShare && (
              <ScaleOnPress onPress={onShare}>
                <View style={[styles.headerButton, { backgroundColor: theme.colors.primary }]}>
                  <Icon name="share" size={20} color="#FFFFFF" />
                </View>
              </ScaleOnPress>
            )}
            {onDelete && (
              <ScaleOnPress onPress={onDelete}>
                <View style={[styles.headerButton, { backgroundColor: theme.colors.error }]}>
                  <Icon name={IconNames.DELETE} size={20} color="#FFFFFF" />
                </View>
              </ScaleOnPress>
            )}
          </View>
        </View>
      </View>

      {/* Content */}
      <View style={styles.content}>
        {renderContent()}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  previewHeader: {
    paddingTop: 50,
    paddingBottom: 16,
    paddingHorizontal: 16,
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  fileInfo: {
    flex: 1,
    marginHorizontal: 16,
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  content: {
    flex: 1,
  },
  imageContainer: {
    flex: 1,
  },
  imageContentContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: screenHeight - 150,
  },
  image: {
    width: screenWidth,
    height: screenHeight - 150,
  },
  videoContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  video: {
    width: screenWidth,
    height: screenHeight - 150,
  },
  textContainer: {
    flex: 1,
    padding: 16,
  },
  textContent: {
    fontFamily: 'monospace',
    lineHeight: 20,
  },
  unsupportedContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 32,
  },
  unsupportedText: {
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  fileTypeText: {
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 32,
  },
  errorText: {
    marginTop: 16,
    marginBottom: 24,
    textAlign: 'center',
  },
  retryButton: {
    minWidth: 100,
  },
});
