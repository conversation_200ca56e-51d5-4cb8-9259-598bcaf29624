import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ViewStyle,
} from 'react-native';
import { DeviceFileSystemService } from '../services/DeviceFileSystemService';
import { useTheme } from '../theme';
import { Text } from './Text';
import { Icon } from './Icon';
import { FadeInView } from './FadeInView';

// const { width } = Dimensions.get('window');

interface StorageInfoCardProps {
  style?: ViewStyle | ViewStyle[];
}

export const StorageInfoCard: React.FC<StorageInfoCardProps> = ({ style }) => {
  const { theme } = useTheme();
  const [storageInfo, setStorageInfo] = useState<{
    free: number;
    total: number;
    used: number;
  } | null>(null);
  const [loading, setLoading] = useState(true);

  const deviceFileSystemService = DeviceFileSystemService.getInstance();

  useEffect(() => {
    loadStorageInfo();
  }, []);

  const loadStorageInfo = async () => {
    try {
      const info = await deviceFileSystemService.getDeviceStorageInfo();
      setStorageInfo(info);
    } catch (error) {
      console.error('Failed to load storage info:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const getUsagePercentage = (): number => {
    if (!storageInfo) return 0;
    return (storageInfo.used / storageInfo.total) * 100;
  };

  const getUsageColor = (): string => {
    const percentage = getUsagePercentage();
    if (percentage > 90) return theme.colors.error;
    if (percentage > 75) return theme.colors.warning;
    return theme.colors.success;
  };

  if (loading || !storageInfo) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.card }, style]}>
        <View style={styles.loadingContainer}>
          <Icon name="hard-drive" size={24} color={theme.colors.textSecondary} />
          <Text variant="caption" color="textSecondary">
            Loading storage info...
          </Text>
        </View>
      </View>
    );
  }

  return (
    <FadeInView style={StyleSheet.flatten([styles.container, { backgroundColor: theme.colors.card }, style || {}])}>
      <View style={styles.header}>
        <View style={styles.iconContainer}>
          <Icon name="hard-drive" size={24} color={theme.colors.primary} />
        </View>
        <View style={styles.headerText}>
          <Text variant="body" color="text" weight="medium">
            Device Storage
          </Text>
          <Text variant="caption" color="textSecondary">
            {formatBytes(storageInfo.used)} of {formatBytes(storageInfo.total)} used
          </Text>
        </View>
        <Text variant="caption" color="textSecondary">
          {Math.round(getUsagePercentage())}%
        </Text>
      </View>

      <View style={styles.progressContainer}>
        <View style={[styles.progressBar, { backgroundColor: theme.colors.border }]}>
          <View
            style={[
              styles.progressFill,
              {
                width: `${getUsagePercentage()}%`,
                backgroundColor: getUsageColor(),
              },
            ]}
          />
        </View>
      </View>

      <View style={styles.details}>
        <View style={styles.detailItem}>
          <View style={[styles.detailIndicator, { backgroundColor: getUsageColor() }]} />
          <Text variant="caption" color="textSecondary">
            Used: {formatBytes(storageInfo.used)}
          </Text>
        </View>
        <View style={styles.detailItem}>
          <View style={[styles.detailIndicator, { backgroundColor: theme.colors.border }]} />
          <Text variant="caption" color="textSecondary">
            Free: {formatBytes(storageInfo.free)}
          </Text>
        </View>
      </View>
    </FadeInView>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    padding: 16,
    marginHorizontal: 16,
    marginVertical: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    gap: 8,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  headerText: {
    flex: 1,
  },
  progressContainer: {
    marginBottom: 12,
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  details: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  detailIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
});
