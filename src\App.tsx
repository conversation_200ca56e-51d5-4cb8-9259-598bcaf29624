import React, { useState, useEffect } from 'react';
import {
  SafeAreaView,
  StyleSheet,
  View,
  BackHandler,
  Alert,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { ThemeProvider, useTheme } from './theme';
import { HomeScreen } from './screens/HomeScreen';
import { StatisticsScreen } from './screens/StatisticsScreen';
import { FolderViewScreen } from './screens/FolderViewScreen';
import { DeviceFileManagerScreen } from './screens/DeviceFileManagerScreen';
import { ChatScreen } from './screens/ChatScreen';
import { SlideUpPanel } from './components/SlideUpPanel';
import { ScaleOnPress } from './components/ScaleOnPress';
import { Icon, IconNames } from './components/Icon';
import { FileItem, ChatMessage } from './types';
import { ChatService } from './services/ChatService';

type Screen = 'home' | 'statistics' | 'folder' | 'device-files' | 'chat';

const MainApp: React.FC = () => {
  const { theme, isDark, toggleTheme } = useTheme();
  const [currentScreen, setCurrentScreen] = useState<Screen>('home');
  const [chatVisible, setChatVisible] = useState(false);
  const [messages, setMessages] = useState<ChatMessage[]>([]);

  const chatService = ChatService.getInstance();

  // Handle Android back button
  useEffect(() => {
    const backAction = () => {
      if (currentScreen === 'home') {
        // If on home screen, show exit confirmation
        Alert.alert(
          'Exit App',
          'Are you sure you want to exit?',
          [
            {
              text: 'Cancel',
              onPress: () => null,
              style: 'cancel',
            },
            {
              text: 'Exit',
              onPress: () => BackHandler.exitApp(),
            },
          ]
        );
        return true; // Prevent default behavior
      } else {
        // Navigate back to home screen
        if (isDark && (currentScreen === 'statistics' || currentScreen === 'folder')) {
          toggleTheme(); // Switch back to light theme
        }
        setCurrentScreen('home');
        return true; // Prevent default behavior
      }
    };

    const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);

    return () => backHandler.remove();
  }, [currentScreen, isDark, toggleTheme]);

  const handleFileSelect = (file: FileItem) => {
    // Handle file selection - could open preview, etc.
    console.log('File selected:', file.name);
  };

  const handleSendMessage = async (messageText: string) => {
    try {
      await chatService.sendMessage(messageText);
      // Reload messages from service
      const updatedMessages = await chatService.getMessageHistory();
      setMessages(updatedMessages);
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };

  const renderCurrentScreen = () => {
    switch (currentScreen) {
      case 'home':
        return (
          <HomeScreen
            onNavigateToStatistics={() => {
              toggleTheme(); // Switch to dark theme for statistics
              setCurrentScreen('statistics');
            }}
            onNavigateToFolder={() => {
              if (!isDark) toggleTheme(); // Switch to dark theme for folder view
              setCurrentScreen('folder');
            }}
            onNavigateToDeviceFiles={() => setCurrentScreen('device-files')}
            onNavigateToChat={() => setCurrentScreen('chat')}
          />
        );
      case 'statistics':
        return (
          <StatisticsScreen
            onBack={() => {
              if (isDark) toggleTheme(); // Switch back to light theme
              setCurrentScreen('home');
            }}
          />
        );
      case 'folder':
        return (
          <FolderViewScreen
            onBack={() => {
              if (isDark) toggleTheme(); // Switch back to light theme
              setCurrentScreen('home');
            }}
          />
        );
      case 'device-files':
        return <DeviceFileManagerScreen onFileSelect={handleFileSelect} />;
      case 'chat':
        return (
          <ChatScreen
            isVisible={true}
            messages={messages}
            onSendMessage={handleSendMessage}
            onClose={() => setCurrentScreen('home')}
          />
        );
      default:
        return (
          <HomeScreen
            onNavigateToStatistics={() => {
              toggleTheme();
              setCurrentScreen('statistics');
            }}
            onNavigateToFolder={() => {
              if (!isDark) toggleTheme();
              setCurrentScreen('folder');
            }}
            onNavigateToDeviceFiles={() => setCurrentScreen('device-files')}
            onNavigateToChat={() => setCurrentScreen('chat')}
          />
        );
    }
  };

  const loadMessages = async () => {
    try {
      const history = await chatService.getMessageHistory();
      setMessages(history);
    } catch (error) {
      console.error('Failed to load messages:', error);
    }
  };

  React.useEffect(() => {
    if (chatVisible) {
      loadMessages();
    }
  }, [chatVisible]);

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <StatusBar style={isDark ? 'light' : 'dark'} />

      <View style={styles.content}>
        {renderCurrentScreen()}
      </View>

      {/* Chat Panel - Only show on certain screens */}
      {(currentScreen === 'device-files' || currentScreen === 'chat') && (
        <>
          <SlideUpPanel
            visible={chatVisible}
            onClose={() => setChatVisible(false)}
            height="80%"
          >
            <ChatScreen
              isVisible={chatVisible}
              onClose={() => setChatVisible(false)}
              messages={messages}
              onSendMessage={handleSendMessage}
            />
          </SlideUpPanel>

          <ScaleOnPress onPress={() => setChatVisible(true)}>
            <View style={[styles.chatFab, { backgroundColor: theme.colors.primary }]}>
              <Icon name={IconNames.CHAT} size={24} color="#FFFFFF" />
            </View>
          </ScaleOnPress>
        </>
      )}

      {/* Demo Navigation - Only show on home screen */}
      {currentScreen === 'home' && (
        <View style={styles.demoNav}>
          <ScaleOnPress
            style={[styles.demoButton, { backgroundColor: theme.colors.surface }]}
            onPress={() => setCurrentScreen('device-files')}
          >
            <Icon name={IconNames.FOLDER} size={20} color={theme.colors.primary} />
          </ScaleOnPress>
          <ScaleOnPress
            style={[styles.demoButton, { backgroundColor: theme.colors.surface }]}
            onPress={() => setCurrentScreen('chat')}
          >
            <Icon name={IconNames.CHAT} size={20} color={theme.colors.primary} />
          </ScaleOnPress>
        </View>
      )}
    </SafeAreaView>
  );
};

function App(): React.JSX.Element {
  return (
    <ThemeProvider>
      <MainApp />
    </ThemeProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  chatFab: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  demoNav: {
    position: 'absolute',
    bottom: 100,
    right: 20,
    gap: 12,
  },
  demoButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
});

export default App;