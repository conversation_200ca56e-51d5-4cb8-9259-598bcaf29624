# Installation Guide

## Quick Setup

Follow these steps to get the File Manager Chat App running with Expo:

### 1. Install Expo CLI
```bash
npm install -g @expo/cli
```

### 2. Clear npm cache and install dependencies
```bash
# Clear npm cache
npm cache clean --force

# Remove node_modules and package-lock.json if they exist
rm -rf node_modules package-lock.json

# Install dependencies
npm install
```

### 3. If you encounter dependency conflicts, use:
```bash
npm install --legacy-peer-deps
```

### 4. Set up environment variables
- Copy `.env.example` to `.env`
- Add your Azure OpenAI credentials

### 5. Start the development server
```bash
npm start
```

## Troubleshooting

### Dependency Conflicts
If you see ERESOLVE errors, try:
```bash
npm install --legacy-peer-deps --force
```

### Expo CLI Issues
If expo command is not recognized:
```bash
# Uninstall old expo-cli
npm uninstall -g expo-cli

# Install new Expo CLI
npm install -g @expo/cli
```

### Metro Bundler Issues
If you encounter Metro bundler issues:
```bash
# Clear Metro cache
npx expo start --clear
```

## Development Commands

- `npm start` - Start Expo development server
- `npm run android` - Run on Android emulator
- `npm run ios` - Run on iOS simulator  
- `npm run web` - Run in web browser
- `npm run lint` - Run ESLint
- `npm run typecheck` - Run TypeScript type checking

## Building for Production

### Using EAS Build (Recommended)
```bash
# Install EAS CLI
npm install -g eas-cli

# Configure EAS
eas build:configure

# Build for Android
npm run build:android

# Build for iOS
npm run build:ios
```

## Platform-Specific Setup

### iOS
- Requires Xcode and iOS Simulator
- No additional setup needed for development

### Android
- Requires Android Studio and Android emulator
- Enable Developer Options and USB Debugging on physical device

### Web
- Works in any modern web browser
- No additional setup needed