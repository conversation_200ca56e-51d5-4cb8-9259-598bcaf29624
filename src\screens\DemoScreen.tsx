import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  StatusBar,
} from 'react-native';
import { useTheme } from '../theme';
import { Text } from '../components/Text';
import { 
  Card, 
  IconButton, 
  Chip, 
  Avatar, 
  ListItem,
  FloatingNav,
  ProgressRing,
  MultiRingProgress,
} from '../components/design-system';
import { Icon, IconNames } from '../components/Icon';
import { ScaleOnPress } from '../components/ScaleOnPress';

export const DemoScreen: React.FC = () => {
  const { theme, toggleTheme, isDark } = useTheme();
  const [selectedChip, setSelectedChip] = useState<string>('default');

  const demoRings = [
    {
      value: 432,
      total: 860,
      color: theme.colors.dataColors.pink,
      thickness: 16,
      label: 'Total',
    },
    {
      value: 219,
      total: 860,
      color: theme.colors.dataColors.pink,
      thickness: 14,
      label: 'Pictures',
    },
    {
      value: 165,
      total: 860,
      color: theme.colors.dataColors.yellow,
      thickness: 14,
      label: 'Documents',
    },
    {
      value: 48,
      total: 860,
      color: theme.colors.dataColors.blue,
      thickness: 14,
      label: 'Videos',
    },
  ];

  const navItems = [
    {
      icon: IconNames.FOLDER,
      onPress: () => console.log('Files'),
      active: true,
    },
    {
      icon: IconNames.SEARCH,
      onPress: () => console.log('Search'),
      active: false,
    },
    {
      icon: IconNames.SETTINGS,
      onPress: () => console.log('Settings'),
      active: false,
    },
  ];

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <StatusBar 
        barStyle={isDark ? 'light-content' : 'dark-content'} 
        backgroundColor="transparent" 
        translucent 
      />

      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <Text variant="title" color="text" weight="bold">
            Design System Demo
          </Text>
          <IconButton
            icon={isDark ? IconNames.SETTINGS : IconNames.SETTINGS}
            onPress={toggleTheme}
            variant="secondary"
          />
        </View>

        {/* Theme Info */}
        <Card style={styles.section} elevation="x1">
          <Text variant="body" color="text" weight="bold" style={styles.sectionTitle}>
            Current Theme: {isDark ? 'Dark' : 'Light'}
          </Text>
          <Text variant="caption" color="textSecondary">
            Tap the settings icon to toggle themes
          </Text>
        </Card>

        {/* Buttons Section */}
        <Card style={styles.section} elevation="x1">
          <Text variant="body" color="text" weight="bold" style={styles.sectionTitle}>
            Icon Buttons
          </Text>
          <View style={styles.buttonRow}>
            <IconButton
              icon={IconNames.PLUS}
              onPress={() => console.log('Add')}
              variant="primary"
              size="small"
            />
            <IconButton
              icon={IconNames.SEARCH}
              onPress={() => console.log('Search')}
              variant="secondary"
              size="medium"
            />
            <IconButton
              icon={IconNames.DELETE}
              onPress={() => console.log('Delete')}
              variant="ghost"
              size="large"
            />
          </View>
        </Card>

        {/* Chips Section */}
        <Card style={styles.section} elevation="x1">
          <Text variant="body" color="text" weight="bold" style={styles.sectionTitle}>
            Chips
          </Text>
          <View style={styles.chipRow}>
            <Chip
              label="Default"
              selected={selectedChip === 'default'}
              onPress={() => setSelectedChip('default')}
            />
            <Chip
              label="Primary"
              variant="primary"
              selected={selectedChip === 'primary'}
              onPress={() => setSelectedChip('primary')}
            />
            <Chip
              label="With Icon"
              icon={IconNames.IMAGE}
              selected={selectedChip === 'icon'}
              onPress={() => setSelectedChip('icon')}
            />
          </View>
        </Card>

        {/* Avatars Section */}
        <Card style={styles.section} elevation="x1">
          <Text variant="body" color="text" weight="bold" style={styles.sectionTitle}>
            Avatars
          </Text>
          <View style={styles.avatarRow}>
            <Avatar name="John Doe" size="small" />
            <Avatar name="Jane Smith" size="medium" />
            <Avatar name="Bob Wilson" size="large" />
          </View>
        </Card>

        {/* Progress Rings Section */}
        <Card style={styles.section} elevation="x1">
          <Text variant="body" color="text" weight="bold" style={styles.sectionTitle}>
            Progress Rings
          </Text>
          <View style={styles.progressRow}>
            <ProgressRing
              value={75}
              total={100}
              size={80}
              color={theme.colors.dataColors.pink}
              animated={true}
            >
              <Text variant="caption" color="text" weight="bold">75%</Text>
            </ProgressRing>
            
            <MultiRingProgress
              rings={demoRings.slice(0, 3)}
              size={120}
              animated={true}
            >
              <View style={styles.centerLabel}>
                <Text variant="body" color="text" weight="bold">432</Text>
                <Text variant="caption" color="textSecondary">GB</Text>
              </View>
            </MultiRingProgress>
          </View>
        </Card>

        {/* List Items Section */}
        <Card style={styles.section} elevation="x1">
          <Text variant="body" color="text" weight="bold" style={styles.sectionTitle}>
            List Items
          </Text>
          <View style={styles.listContainer}>
            <ListItem
              title="IMG2075.jpg"
              subtitle="08/12/2023 · 3:38 AM"
              leftIcon={IconNames.IMAGE}
              rightIcon={IconNames.MORE}
              accentColor={theme.colors.dataColors.pink}
              onPress={() => console.log('Image file')}
            />
            <ListItem
              title="Document.pdf"
              subtitle="Yesterday · 2:15 PM"
              leftIcon={IconNames.FILE_TEXT}
              rightText="2.4 MB"
              accentColor={theme.colors.dataColors.yellow}
              onPress={() => console.log('PDF file')}
              selected={true}
            />
            <ListItem
              title="Video.mp4"
              subtitle="Last week"
              leftIcon={IconNames.VIDEO}
              rightIcon={IconNames.MORE}
              accentColor={theme.colors.dataColors.blue}
              onPress={() => console.log('Video file')}
            />
          </View>
        </Card>

        {/* Typography Section */}
        <Card style={styles.section} elevation="x1">
          <Text variant="body" color="text" weight="bold" style={styles.sectionTitle}>
            Typography Scale
          </Text>
          <View style={styles.typographyContainer}>
            <Text style={[theme.typography.display.large, { color: theme.colors.text }]}>
              Display Large
            </Text>
            <Text style={[theme.typography.title.medium, { color: theme.colors.text }]}>
              Title Medium
            </Text>
            <Text variant="body" color="text">Body Text</Text>
            <Text variant="caption" color="textSecondary">Caption Text</Text>
          </View>
        </Card>

        {/* Haptic Feedback Demo */}
        <Card style={styles.section} elevation="x1">
          <Text variant="body" color="text" weight="bold" style={styles.sectionTitle}>
            Haptic Feedback
          </Text>
          <View style={styles.hapticRow}>
            <ScaleOnPress
              style={[styles.hapticButton, { backgroundColor: theme.colors.primary }]}
              onPress={() => console.log('Light haptic')}
              hapticFeedback="light"
            >
              <Text variant="caption" color="white" weight="medium">Light</Text>
            </ScaleOnPress>
            <ScaleOnPress
              style={[styles.hapticButton, { backgroundColor: theme.colors.dataColors.yellow }]}
              onPress={() => console.log('Medium haptic')}
              hapticFeedback="medium"
            >
              <Text variant="caption" color="text" weight="medium">Medium</Text>
            </ScaleOnPress>
            <ScaleOnPress
              style={[styles.hapticButton, { backgroundColor: theme.colors.error }]}
              onPress={() => console.log('Heavy haptic')}
              hapticFeedback="heavy"
            >
              <Text variant="caption" color="white" weight="medium">Heavy</Text>
            </ScaleOnPress>
          </View>
        </Card>
      </ScrollView>

      {/* Floating Navigation */}
      <FloatingNav items={navItems} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 120,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    marginBottom: 16,
  },
  buttonRow: {
    flexDirection: 'row',
    gap: 12,
    alignItems: 'center',
  },
  chipRow: {
    flexDirection: 'row',
    gap: 8,
    flexWrap: 'wrap',
  },
  avatarRow: {
    flexDirection: 'row',
    gap: 12,
    alignItems: 'center',
  },
  progressRow: {
    flexDirection: 'row',
    gap: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  centerLabel: {
    alignItems: 'center',
  },
  listContainer: {
    backgroundColor: 'transparent',
  },
  typographyContainer: {
    gap: 8,
  },
  hapticRow: {
    flexDirection: 'row',
    gap: 12,
  },
  hapticButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
});
