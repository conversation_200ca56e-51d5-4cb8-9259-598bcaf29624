import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  RefreshControl,
  Alert,
  Dimensions,
} from 'react-native';
import { FileManagerScreenProps, FileItem } from '../types';
import { useTheme } from '../theme';
import { DeviceFileSystemService, DeviceDirectory } from '../services/DeviceFileSystemService';
import { FileSystemService } from '../services/FileSystemService';
import { FileTypeService } from '../services/FileTypeService';
import { SearchService } from '../services/SearchService';
import { Text } from '../components/Text';

import { Input } from '../components/Input';
import { Icon, IconNames } from '../components/Icon';
import { FileListItem } from '../components/FileListItem';
import { FadeInView } from '../components/FadeInView';
import { ScaleOnPress } from '../components/ScaleOnPress';
import { FilePreviewModal } from '../components/FilePreviewModal';
import { StorageInfoCard } from '../components/StorageInfoCard';
import { FileOperationsService } from '../services/FileOperationsService';

const { width: screenWidth } = Dimensions.get('window');

export const DeviceFileManagerScreen: React.FC<FileManagerScreenProps> = ({
  initialPath,
  onFileSelect,
}) => {
  const { theme } = useTheme();
  const [currentPath, setCurrentPath] = useState(initialPath || '');
  const [files, setFiles] = useState<FileItem[]>([]);
  const [deviceDirectories, setDeviceDirectories] = useState<DeviceDirectory[]>([]);
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [viewMode, ] = useState<'list' | 'grid' | 'detailed'>('grid');
  const [sortBy, ] = useState<'name' | 'date' | 'size' | 'type'>('date');
  const [sortOrder, ] = useState<'asc' | 'desc'>('desc');
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearch, setShowSearch] = useState(false);
  const [navigationHistory, setNavigationHistory] = useState<string[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [showDirectorySelector, setShowDirectorySelector] = useState(true);
  const [previewFile, setPreviewFile] = useState<FileItem | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const [permissionsGranted, setPermissionsGranted] = useState(false);
  const [permissionLoading, setPermissionLoading] = useState(true);

  const deviceFileSystemService = DeviceFileSystemService.getInstance();
  const fileSystemService = FileSystemService.getInstance();
  const fileTypeService = FileTypeService.getInstance();
  const searchService = SearchService.getInstance();
  const fileOperationsService = FileOperationsService.getInstance();

  // Check and request permissions
  const checkPermissions = useCallback(async () => {
    try {
      setPermissionLoading(true);
      // Use improved permission handling
      const hasPermissions = await deviceFileSystemService.ensureMediaPermissions(true);
      setPermissionsGranted(hasPermissions);
      if (hasPermissions) {
        await loadDeviceDirectories();
      }
    } catch (error) {
      console.error('Failed to get permissions:', error);
      setPermissionsGranted(false);
    } finally {
      setPermissionLoading(false);
    }
  }, [deviceFileSystemService, loadDeviceDirectories]);

  // Load device directories
  const loadDeviceDirectories = useCallback(async () => {
    try {
      const directories = await deviceFileSystemService.getDeviceDirectories();
      setDeviceDirectories(directories);
    } catch (error) {
      console.error('Failed to load device directories:', error);
    }
  }, [deviceFileSystemService]);

  // Load directory contents
  const loadDirectory = useCallback(async (path: string, addToHistory = true) => {
    try {
      setLoading(true);
      setShowDirectorySelector(false);
      
      let directoryFiles: FileItem[];
      
      if (path.startsWith('media://')) {
        directoryFiles = await deviceFileSystemService.listDirectory(path);
      } else {
        directoryFiles = await fileSystemService.listDirectory(path);
      }
      
      const sortedFiles = fileTypeService.sortFiles(directoryFiles, sortBy, sortOrder);
      
      setFiles(sortedFiles);
      setCurrentPath(path);
      setSelectedFiles([]);

      // Update navigation history
      if (addToHistory) {
        const newHistory = navigationHistory.slice(0, historyIndex + 1);
        newHistory.push(path);
        setNavigationHistory(newHistory);
        setHistoryIndex(newHistory.length - 1);
      }

      // Index directory for search (skip media:// paths due to Expo Go limitations)
      if (!path.startsWith('media://')) {
        searchService.indexDirectory(path);
      }
    } catch (error) {
      console.error('Failed to load directory:', error);
      Alert.alert('Error', 'Failed to load directory contents');
    } finally {
      setLoading(false);
    }
  }, [sortBy, sortOrder, navigationHistory, historyIndex, deviceFileSystemService, fileSystemService, searchService, fileTypeService]);

  // Initial load
  useEffect(() => {
    checkPermissions();
  }, [checkPermissions]);

  // Load directory when path changes
  useEffect(() => {
    if (currentPath && permissionsGranted) {
      loadDirectory(currentPath, false);
    }
  }, [currentPath, permissionsGranted, loadDirectory]);

  // Handle directory selection
  const handleDirectorySelect = useCallback((directory: DeviceDirectory) => {
    if (directory.accessible) {
      loadDirectory(directory.path);
    } else {
      Alert.alert('Access Denied', 'This directory is not accessible');
    }
  }, [loadDirectory]);

  // Handle back navigation
  const handleBack = useCallback(() => {
    if (historyIndex > 0) {
      const previousPath = navigationHistory[historyIndex - 1];
      setHistoryIndex(historyIndex - 1);
      loadDirectory(previousPath, false);
    } else {
      // Go back to directory selector
      setShowDirectorySelector(true);
      setCurrentPath('');
      setFiles([]);
    }
  }, [historyIndex, navigationHistory, loadDirectory]);

  // Handle file press
  const handleFilePress = useCallback((file: FileItem) => {
    if (file.type === 'directory') {
      loadDirectory(file.path);
    } else {
      // Check if file can be previewed
      const canPreview = file.mimeType?.startsWith('image/') ||
                        file.mimeType?.startsWith('video/') ||
                        file.mimeType?.startsWith('text/') ||
                        file.name.endsWith('.json') ||
                        file.name.endsWith('.md');

      if (canPreview) {
        setPreviewFile(file);
        setShowPreview(true);
      } else if (onFileSelect) {
        onFileSelect(file);
      }

      searchService.addToRecentFiles(file);
    }
  }, [loadDirectory, onFileSelect, searchService]);

  // Handle refresh
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    if (showDirectorySelector) {
      await loadDeviceDirectories();
    } else {
      await loadDirectory(currentPath, false);
    }
    setRefreshing(false);
  }, [currentPath, loadDirectory, loadDeviceDirectories, showDirectorySelector]);

  // Handle camera action
  const handleTakePhoto = useCallback(async () => {
    try {
      const photo = await deviceFileSystemService.takePhoto();
      if (photo) {
        // Refresh current directory to show new photo
        await handleRefresh();
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to take photo');
    }
  }, [deviceFileSystemService, handleRefresh]);

  // Render directory selector
  const renderDirectorySelector = () => (
    <View style={styles.directorySelectorContainer}>
      <Text variant="heading" color="text" weight="bold" style={styles.selectorTitle}>
        Choose Location
      </Text>
      <Text variant="body" color="textSecondary" style={styles.selectorSubtitle}>
        Select where you want to browse files
      </Text>

      <StorageInfoCard style={styles.storageCard} />

      <FlatList
        data={deviceDirectories}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <ScaleOnPress onPress={() => handleDirectorySelect(item)}>
            <View style={[
              styles.directoryCard,
              { 
                backgroundColor: theme.colors.card,
                borderColor: theme.colors.border,
                opacity: item.accessible ? 1 : 0.5,
              }
            ]}>
              <View style={[styles.directoryIcon, { backgroundColor: theme.colors.primary + '20' }]}>
                <Icon name={item.icon} size={32} color={theme.colors.primary} />
              </View>
              <View style={styles.directoryInfo}>
                <Text variant="body" color="text" weight="medium">
                  {item.name}
                </Text>
                <Text variant="caption" color="textSecondary" numberOfLines={2}>
                  {item.description}
                </Text>
              </View>
              <Icon name="chevron-right" size={20} color={theme.colors.textSecondary} />
            </View>
          </ScaleOnPress>
        )}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.directoryList}
      />
    </View>
  );

  // Render header for file view
  const renderFileHeader = () => (
    <View style={[styles.header, { backgroundColor: theme.colors.surface }]}>
      <View style={styles.headerTop}>
        <View style={styles.navigationControls}>
          <ScaleOnPress onPress={handleBack}>
            <View style={[styles.navButton, { backgroundColor: theme.colors.primary }]}>
              <Icon name={IconNames.BACK} size={20} color="#FFFFFF" />
            </View>
          </ScaleOnPress>
        </View>

        <View style={styles.headerActions}>
          <ScaleOnPress onPress={() => setShowSearch(!showSearch)}>
            <View style={[styles.actionButton, { backgroundColor: theme.colors.primary }]}>
              <Icon name={IconNames.SEARCH} size={20} color="#FFFFFF" />
            </View>
          </ScaleOnPress>
          
          <ScaleOnPress onPress={handleTakePhoto}>
            <View style={[styles.actionButton, { backgroundColor: theme.colors.secondary }]}>
              <Icon name="camera" size={20} color="#FFFFFF" />
            </View>
          </ScaleOnPress>
        </View>
      </View>

      {showSearch && (
        <FadeInView direction="down" style={styles.searchContainer}>
          <Input
            placeholder="Search files..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            variant="filled"
          />
        </FadeInView>
      )}

      <View style={styles.pathContainer}>
        <Text variant="caption" color="textSecondary" numberOfLines={1}>
          {currentPath.replace('media://', '').replace(/\//g, ' › ') || 'Device Storage'}
        </Text>
      </View>
    </View>
  );

  // Render permission request screen
  const renderPermissionRequest = () => (
    <View style={styles.permissionContainer}>
      <FadeInView style={styles.permissionContent}>
        <Icon name="shield" size={64} color={theme.colors.primary} />
        <Text variant="heading" color="text" weight="bold" style={styles.permissionTitle}>
          File Access Required
        </Text>
        <Text variant="body" color="textSecondary" style={styles.permissionDescription}>
          To browse and manage your files, we need permission to access your device storage and media library.
        </Text>
        <ScaleOnPress onPress={checkPermissions} disabled={permissionLoading}>
          <View style={[styles.permissionButton, { backgroundColor: theme.colors.primary }]}>
            <Text variant="body" color="white" weight="medium">
              {permissionLoading ? 'Requesting...' : 'Grant Permissions'}
            </Text>
          </View>
        </ScaleOnPress>
      </FadeInView>
    </View>
  );

  if (permissionLoading) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.loadingContainer}>
          <Text variant="body" color="textSecondary">Loading...</Text>
        </View>
      </View>
    );
  }

  if (!permissionsGranted) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        {renderPermissionRequest()}
      </View>
    );
  }

  if (showDirectorySelector) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        {renderDirectorySelector()}
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {renderFileHeader()}
      
      <FlatList
        data={files}
        keyExtractor={(item) => item.path}
        renderItem={({ item }) => (
          <FileListItem
            item={item}
            viewMode={viewMode}
            onPress={() => handleFilePress(item)}
            onLongPress={() => {}}
            isSelected={selectedFiles.includes(item.path)}
          />
        )}
        numColumns={viewMode === 'grid' ? Math.floor(screenWidth / 120) : 1}
        key={`${viewMode}-${Math.floor(screenWidth / 120)}`}
        contentContainerStyle={files.length === 0 ? styles.emptyContainer : undefined}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor={theme.colors.primary}
          />
        }
        showsVerticalScrollIndicator={false}
      />

      {/* File Preview Modal */}
      <FilePreviewModal
        visible={showPreview}
        file={previewFile}
        onClose={() => {
          setShowPreview(false);
          setPreviewFile(null);
        }}
        onShare={async () => {
          if (previewFile) {
            await fileOperationsService.shareFile(previewFile);
          }
        }}
        onDelete={async () => {
          if (previewFile) {
            Alert.alert(
              'Delete File',
              `Are you sure you want to delete "${previewFile.name}"?`,
              [
                { text: 'Cancel', style: 'cancel' },
                {
                  text: 'Delete',
                  style: 'destructive',
                  onPress: async () => {
                    await fileOperationsService.deleteFiles([previewFile]);
                    setShowPreview(false);
                    setPreviewFile(null);
                    await handleRefresh();
                  },
                },
              ]
            );
          }
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  directorySelectorContainer: {
    flex: 1,
    paddingTop: 60,
    paddingHorizontal: 20,
  },
  selectorTitle: {
    marginBottom: 8,
  },
  selectorSubtitle: {
    marginBottom: 32,
  },
  directoryList: {
    gap: 12,
  },
  directoryCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 16,
    borderWidth: 1,
  },
  directoryIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  directoryInfo: {
    flex: 1,
  },
  header: {
    paddingTop: 50,
    paddingBottom: 16,
    paddingHorizontal: 16,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  navigationControls: {
    flexDirection: 'row',
  },
  navButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  searchContainer: {
    marginBottom: 12,
  },
  pathContainer: {
    paddingHorizontal: 4,
  },
  emptyContainer: {
    flex: 1,
  },
  storageCard: {
    marginBottom: 16,
    marginHorizontal: 0,
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  permissionContent: {
    alignItems: 'center',
    maxWidth: 300,
  },
  permissionTitle: {
    marginTop: 24,
    marginBottom: 12,
    textAlign: 'center',
  },
  permissionDescription: {
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 32,
  },
  permissionButton: {
    paddingHorizontal: 32,
    paddingVertical: 12,
    borderRadius: 24,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
