{"name": "file-manager-chat-app", "version": "1.0.0", "main": "App.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "lint": "eslint .", "test": "jest", "typecheck": "tsc --noEmit"}, "dependencies": {"@langchain/core": "^0.3.21", "@langchain/openai": "^0.3.12", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "dotenv": "^17.2.1", "expo": "~53.0.0", "expo-av": "~15.1.7", "expo-constants": "~17.1.7", "expo-document-picker": "~13.1.6", "expo-file-system": "~18.1.11", "expo-haptics": "~14.1.4", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-media-library": "~17.1.7", "expo-secure-store": "~14.2.3", "expo-sharing": "~13.1.5", "expo-sqlite": "~15.2.14", "expo-status-bar": "~2.2.3", "langchain": "^0.3.7", "react": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-linear-gradient": "^2.8.3", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "^15.12.1", "zustand": "^4.4.7", "expo-dev-client": "~5.2.4", "expo-blur": "~14.1.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^12.4.2", "@types/jest": "^30.0.0", "@types/react": "~19.0.10", "@types/react-native": "^0.73.0", "eslint": "^8.57.0", "eslint-config-expo": "~9.2.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "jest": "^29.7.0", "jest-expo": "~53.0.0", "prettier": "^3.2.5", "typescript": "~5.8.3"}, "private": true, "engines": {"node": ">=18"}, "jest": {"preset": "jest-expo", "setupFilesAfterEnv": ["@testing-library/jest-native/extend-expect"], "transformIgnorePatterns": ["node_modules/(?!((jest-)?react-native|@react-native(-community)?)|expo(nent)?|@expo(nent)?/.*|@expo-google-fonts/.*|react-navigation|@react-navigation/.*|@unimodules/.*|unimodules|sentry-expo|native-base|react-native-svg)"]}}