import React from 'react';
import { View, TouchableOpacity, ViewStyle, StyleSheet } from 'react-native';
import { BlurView } from 'expo-blur';
import { useTheme } from '../../theme';
import { Icon, IconNames } from '../Icon';
import { ScaleOnPress } from '../ScaleOnPress';
import { AnimatedIcon } from '../AnimatedIcon';

interface NavItem {
  icon: IconNames;
  onPress: () => void;
  active?: boolean;
}

interface FloatingNavProps {
  items: NavItem[];
  style?: ViewStyle;
  position?: 'bottom' | 'top';
}

export const FloatingNav: React.FC<FloatingNavProps> = ({
  items,
  style,
  position = 'bottom',
}) => {
  const { theme, isDark } = useTheme();

  const containerStyle: ViewStyle = {
    height: 56,
    borderRadius: 28,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 12,
    borderWidth: 1,
    borderColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
    overflow: 'hidden',
    ...theme.elevation.x3,
  };

  const positionStyle: ViewStyle = position === 'bottom'
    ? {
        position: 'absolute',
        bottom: 34,
        left: '50%',
        transform: [{ translateX: -((items.length * 48 + 24) / 2) }],
      }
    : {
        position: 'absolute',
        top: 60,
        left: '50%',
        transform: [{ translateX: -((items.length * 48 + 24) / 2) }],
      };

  return (
    <BlurView
      intensity={80}
      tint={isDark ? 'dark' : 'light'}
      style={[containerStyle, positionStyle, style]}
    >
      {items.map((item, index) => (
        <ScaleOnPress
          key={index}
          style={[
            styles.navItem,
            item.active && [styles.activeNavItem, { backgroundColor: theme.colors.primary + '30' }],
          ]}
          onPress={item.onPress}
        >
          <AnimatedIcon
            name={item.icon}
            size={20}
            color={item.active ? theme.colors.primary : theme.colors.textSecondary}
            animation={item.active ? 'pulse' : 'none'}
            duration={1500}
          />
        </ScaleOnPress>
      ))}
    </BlurView>
  );
};

const styles = StyleSheet.create({
  navItem: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 4,
  },
  activeNavItem: {
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
});
