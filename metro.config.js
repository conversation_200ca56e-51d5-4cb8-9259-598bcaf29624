const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

const config = getDefaultConfig(__dirname);

// Add support for additional file extensions
config.resolver.assetExts.push('db', 'mp3', 'ttf', 'obj');

// Enable symlinks for monorepos
config.resolver.unstable_enableSymlinks = true;

// Add path aliases
config.resolver.alias = {
  '@': path.resolve(__dirname, 'src'),
  '@/components': path.resolve(__dirname, 'src/components'),
  '@/services': path.resolve(__dirname, 'src/services'),
  '@/types': path.resolve(__dirname, 'src/types'),
  '@/utils': path.resolve(__dirname, 'src/utils'),
  '@/stores': path.resolve(__dirname, 'src/stores'),
  '@/screens': path.resolve(__dirname, 'src/screens'),
  '@/theme': path.resolve(__dirname, 'src/theme'),
};

module.exports = config;