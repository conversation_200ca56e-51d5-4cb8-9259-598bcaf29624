/**
 * shadcn/ui Demo Screen
 * Showcases the new design system and components
 */

import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  StatusBar,
  Alert,
} from 'react-native';
import { useTheme } from '../theme';
import { Text } from '../components/Text';
import { Button } from '../components/shadcn/Button';
import { Card, CardHeader, CardContent, CardFooter } from '../components/shadcn/Card';
import { Icon, IconNames } from '../components/Icon';
import { ScaleOnPress } from '../components/ScaleOnPress';

interface ShadcnDemoScreenProps {
  onNavigateBack?: () => void;
}

export const ShadcnDemoScreen: React.FC<ShadcnDemoScreenProps> = ({
  onNavigateBack,
}) => {
  const { theme, isDark, toggleTheme } = useTheme();
  const [loading, setLoading] = useState(false);

  const handleAsyncAction = async () => {
    setLoading(true);
    // Simulate async operation
    await new Promise(resolve => setTimeout(resolve, 2000));
    setLoading(false);
    Alert.alert('Success', 'Action completed successfully!');
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <StatusBar
        barStyle={isDark ? 'light-content' : 'dark-content'}
        backgroundColor={theme.colors.background}
      />
      
      {/* Header */}
      <View style={[styles.header, { borderBottomColor: theme.colors.border }]}>
        <ScaleOnPress onPress={onNavigateBack} style={styles.backButton}>
          <Icon name={IconNames.ARROW_LEFT} size={24} color={theme.colors.text} />
        </ScaleOnPress>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
          shadcn/ui Demo
        </Text>
        <ScaleOnPress onPress={toggleTheme} style={styles.themeButton}>
          <Icon 
            name={isDark ? IconNames.SUN : IconNames.MOON} 
            size={24} 
            color={theme.colors.text} 
          />
        </ScaleOnPress>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Welcome Card */}
        <Card style={styles.card}>
          <CardHeader>
            <Text style={[styles.cardTitle, { color: theme.colors.text }]}>
              Welcome to shadcn/ui
            </Text>
            <Text style={[styles.cardDescription, { color: theme.colors.textSecondary }]}>
              Beautiful, modern components with smooth animations
            </Text>
          </CardHeader>
        </Card>

        {/* Button Variants */}
        <Card style={styles.card}>
          <CardHeader>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              Button Variants
            </Text>
          </CardHeader>
          <CardContent>
            <View style={styles.buttonGrid}>
              <Button onPress={() => Alert.alert('Default', 'Default button pressed')}>
                Default
              </Button>
              <Button 
                variant="secondary" 
                onPress={() => Alert.alert('Secondary', 'Secondary button pressed')}
              >
                Secondary
              </Button>
              <Button 
                variant="outline" 
                onPress={() => Alert.alert('Outline', 'Outline button pressed')}
              >
                Outline
              </Button>
              <Button 
                variant="ghost" 
                onPress={() => Alert.alert('Ghost', 'Ghost button pressed')}
              >
                Ghost
              </Button>
              <Button 
                variant="destructive" 
                onPress={() => Alert.alert('Destructive', 'Destructive button pressed')}
              >
                Destructive
              </Button>
              <Button 
                variant="link" 
                onPress={() => Alert.alert('Link', 'Link button pressed')}
              >
                Link
              </Button>
            </View>
          </CardContent>
        </Card>

        {/* Button Sizes */}
        <Card style={styles.card}>
          <CardHeader>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              Button Sizes
            </Text>
          </CardHeader>
          <CardContent>
            <View style={styles.buttonColumn}>
              <Button size="sm" onPress={() => Alert.alert('Small', 'Small button')}>
                Small Button
              </Button>
              <Button size="default" onPress={() => Alert.alert('Default', 'Default size')}>
                Default Button
              </Button>
              <Button size="lg" onPress={() => Alert.alert('Large', 'Large button')}>
                Large Button
              </Button>
            </View>
          </CardContent>
        </Card>

        {/* Interactive Card */}
        <Card 
          style={styles.card}
          pressable
          onPress={() => Alert.alert('Card Pressed', 'This card is interactive!')}
        >
          <CardHeader>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              Interactive Card
            </Text>
            <Text style={[styles.cardDescription, { color: theme.colors.textSecondary }]}>
              Tap this card to see the press animation
            </Text>
          </CardHeader>
          <CardContent>
            <View style={styles.iconRow}>
              <Icon name={IconNames.TOUCH} size={20} color={theme.colors.primary} />
              <Text style={[styles.iconText, { color: theme.colors.textSecondary }]}>
                Press me!
              </Text>
            </View>
          </CardContent>
        </Card>

        {/* Loading State */}
        <Card style={styles.card}>
          <CardHeader>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              Loading States
            </Text>
          </CardHeader>
          <CardContent>
            <View style={styles.buttonColumn}>
              <Button 
                loading={loading}
                onPress={handleAsyncAction}
                disabled={loading}
              >
                {loading ? 'Loading...' : 'Start Async Action'}
              </Button>
            </View>
          </CardContent>
        </Card>

        {/* Color Showcase */}
        <Card style={styles.card}>
          <CardHeader>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              Color System
            </Text>
          </CardHeader>
          <CardContent>
            <View style={styles.colorGrid}>
              <View style={[styles.colorSwatch, { backgroundColor: theme.colors.primary }]} />
              <View style={[styles.colorSwatch, { backgroundColor: theme.colors.secondary }]} />
              <View style={[styles.colorSwatch, { backgroundColor: theme.colors.success }]} />
              <View style={[styles.colorSwatch, { backgroundColor: theme.colors.warning }]} />
              <View style={[styles.colorSwatch, { backgroundColor: theme.colors.error }]} />
              <View style={[styles.colorSwatch, { backgroundColor: theme.colors.info }]} />
            </View>
          </CardContent>
        </Card>

        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  themeButton: {
    padding: 8,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  card: {
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 4,
  },
  cardDescription: {
    fontSize: 16,
    lineHeight: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  buttonGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  buttonColumn: {
    gap: 12,
  },
  iconRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  iconText: {
    fontSize: 14,
  },
  colorGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  colorSwatch: {
    width: 40,
    height: 40,
    borderRadius: 8,
  },
  bottomSpacing: {
    height: 32,
  },
});
