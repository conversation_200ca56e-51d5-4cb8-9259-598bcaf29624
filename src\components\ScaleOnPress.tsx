import React from 'react';
import { TouchableWithoutFeedback, ViewStyle, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';

interface ScaleOnPressProps {
  children: React.ReactNode;
  onPress?: () => void;
  onLongPress?: () => void;
  style?: ViewStyle | ViewStyle[];
  scaleValue?: number;
  springConfig?: {
    damping?: number;
    stiffness?: number;
  };
  disabled?: boolean;
  hapticFeedback?: 'light' | 'medium' | 'heavy' | 'success' | 'warning' | 'error' | 'none';
}

export const ScaleOnPress: React.FC<ScaleOnPressProps> = ({
  children,
  onPress,
  onLongPress,
  style,
  scaleValue = 0.96,
  springConfig = { damping: 20, stiffness: 160 },
  disabled = false,
  hapticFeedback = 'light',
}) => {
  const scale = useSharedValue(1);

  const triggerHapticFeedback = () => {
    if (hapticFeedback === 'none') return;

    try {
      switch (hapticFeedback) {
        case 'light':
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          break;
        case 'medium':
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
          break;
        case 'heavy':
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
          break;
        case 'success':
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
          break;
        case 'warning':
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
          break;
        case 'error':
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
          break;
      }
    } catch (error) {
      // Haptics not supported on this device
      console.log('Haptics not supported:', error);
    }
  };

  const handlePressIn = () => {
    if (!disabled) {
      scale.value = withSpring(scaleValue, {
        damping: springConfig.damping,
        stiffness: springConfig.stiffness,
      });
      triggerHapticFeedback();
    }
  };

  const handlePressOut = () => {
    if (!disabled) {
      scale.value = withSpring(1, {
        damping: springConfig.damping,
        stiffness: springConfig.stiffness,
      });
    }
  };

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  return (
    <TouchableWithoutFeedback
      onPress={onPress}
      onLongPress={onLongPress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      disabled={disabled}>
      <Animated.View style={[animatedStyle, StyleSheet.flatten(style)]}>
        {children}
      </Animated.View>
    </TouchableWithoutFeedback>
  );
};