import React from 'react';
import { ViewStyle, StyleSheet } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withTiming,
  interpolate,
  Easing,
} from 'react-native-reanimated';

const AnimatedLinearGradient = Animated.createAnimatedComponent(LinearGradient);

interface ModernGradientProps {
  children?: React.ReactNode;
  style?: ViewStyle;
  variant?: 'primary' | 'secondary' | 'accent' | 'glass' | 'aurora' | 'sunset';
  animated?: boolean;
  intensity?: 'subtle' | 'medium' | 'strong';
}

export const ModernGradient: React.FC<ModernGradientProps> = ({
  children,
  style,
  variant = 'primary',
  animated = false,
  intensity = 'medium',
}) => {
  const animationValue = useSharedValue(0);

  React.useEffect(() => {
    if (animated) {
      animationValue.value = withRepeat(
        withTiming(1, { duration: 3000, easing: Easing.inOut(Easing.ease) }),
        -1,
        true
      );
    }
  }, [animated, animationValue]);

  const getGradientColors = () => {
    const intensityMap = {
      subtle: 0.1,
      medium: 0.18,
      strong: 0.3,
    };
    
    const alpha = intensityMap[intensity];

    switch (variant) {
      case 'primary':
        return [
          `rgba(99, 102, 241, ${alpha})`, // Indigo
          `rgba(168, 85, 247, ${alpha})`, // Purple
          `rgba(236, 72, 153, ${alpha})`, // Pink
        ];
      case 'secondary':
        return [
          `rgba(34, 197, 94, ${alpha})`, // Green
          `rgba(59, 130, 246, ${alpha})`, // Blue
          `rgba(147, 51, 234, ${alpha})`, // Purple
        ];
      case 'accent':
        return [
          `rgba(251, 146, 60, ${alpha})`, // Orange
          `rgba(245, 101, 101, ${alpha})`, // Red
          `rgba(236, 72, 153, ${alpha})`, // Pink
        ];
      case 'glass':
        return [
          `rgba(255, 255, 255, ${alpha * 0.8})`,
          `rgba(255, 255, 255, ${alpha * 0.4})`,
          `rgba(255, 255, 255, ${alpha * 0.1})`,
        ];
      case 'aurora':
        return [
          `rgba(16, 185, 129, ${alpha})`, // Emerald
          `rgba(59, 130, 246, ${alpha})`, // Blue
          `rgba(139, 92, 246, ${alpha})`, // Violet
          `rgba(236, 72, 153, ${alpha})`, // Pink
        ];
      case 'sunset':
        return [
          `rgba(251, 146, 60, ${alpha})`, // Orange
          `rgba(252, 211, 77, ${alpha})`, // Yellow
          `rgba(248, 113, 113, ${alpha})`, // Red
          `rgba(236, 72, 153, ${alpha})`, // Pink
        ];
      default:
        return [
          `rgba(99, 102, 241, ${alpha})`,
          `rgba(168, 85, 247, ${alpha})`,
        ];
    }
  };

  const animatedStyle = useAnimatedStyle(() => {
    if (!animated) return {};
    
    const opacity = interpolate(animationValue.value, [0, 1], [0.8, 1.2]);
    const scale = interpolate(animationValue.value, [0, 1], [1, 1.05]);
    
    return {
      opacity,
      transform: [{ scale }],
    };
  });

  const gradientColors = getGradientColors();

  return (
    <AnimatedLinearGradient
      colors={gradientColors}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={[StyleSheet.absoluteFillObject, animatedStyle, style]}
    >
      {children}
    </AnimatedLinearGradient>
  );
};
