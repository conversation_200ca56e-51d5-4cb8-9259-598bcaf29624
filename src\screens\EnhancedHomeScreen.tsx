import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Dimensions,
  StatusBar,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useTheme } from '../theme';
import { Text } from '../components/Text';
import {
  Card,
  IconButton,
  Chip,
  Avatar,
  ListItem,
  FloatingNav
} from '../components/design-system';
import { Icon, IconNames } from '../components/Icon';
import {
  AnimatedIcon,
  ModernGradient,
  GlassCard,
  FadeInView
} from '../components';
import { DeviceFileSystemService } from '../services/DeviceFileSystemService';
import { SearchService } from '../services/SearchService';
import { FileTypeService } from '../services/FileTypeService';
import { FileItem } from '../types';

const { width: screenWidth } = Dimensions.get('window');

interface EnhancedHomeScreenProps {
  onNavigateToStatistics?: () => void;
  onNavigateToFolder?: () => void;
  onNavigateToDeviceFiles?: () => void;
  onNavigateToChat?: () => void;
  onNavigateToSettings?: () => void;
}

export const EnhancedHomeScreen: React.FC<EnhancedHomeScreenProps> = ({
  onNavigateToStatistics,
  onNavigateToFolder,
  onNavigateToDeviceFiles,
  onNavigateToChat,
  onNavigateToSettings,
}) => {
  const { theme } = useTheme();
  const [storageInfo, setStorageInfo] = useState({
    used: 432,
    total: 860,
    usedPercentage: 50.2,
  });
  const [recentFiles, setRecentFiles] = useState<FileItem[]>([]);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      await Promise.all([
        loadStorageInfo(),
        loadRecentFiles(),
      ]);
    } catch (error) {
      console.error('Failed to load home screen data:', error);
    }
  };

  const loadStorageInfo = async () => {
    try {
      const deviceService = DeviceFileSystemService.getInstance();
      const info = await deviceService.getDeviceStorageInfo();
      if (info) {
        setStorageInfo({
          used: Math.round(info.used / (1024 * 1024 * 1024)),
          total: Math.round(info.total / (1024 * 1024 * 1024)),
          usedPercentage: (info.used / info.total) * 100,
        });
      }
    } catch (error) {
      console.error('Failed to load storage info:', error);
    }
  };

  const loadRecentFiles = async () => {
    try {
      const searchService = SearchService.getInstance();
      const files = await searchService.getRecentFiles(5);
      setRecentFiles(files);
    } catch (error) {
      console.error('Failed to load recent files:', error);
    }
  };

  const handleAddFiles = () => {
    onNavigateToDeviceFiles?.();
  };

  const handleFilePress = (file: FileItem) => {
    onNavigateToFolder?.();
  };

  const navItems = [
    {
      icon: IconNames.FOLDER,
      onPress: () => onNavigateToDeviceFiles?.(),
      active: true,
    },
    {
      icon: IconNames.SEARCH,
      onPress: () => onNavigateToChat?.(),
      active: false,
    },
    {
      icon: IconNames.SETTINGS,
      onPress: () => onNavigateToSettings?.(),
      active: false,
    },
  ];

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent />
      
      {/* Background Gradient */}
      <ModernGradient
        variant="aurora"
        intensity="subtle"
        animated={true}
      />

      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* App Header */}
        <FadeInView delay={100} direction="down" style={styles.header}>
          <View style={styles.headerLeft}>
            <Text variant="body" color="textSecondary" style={styles.welcomeText}>
              Welcome Back
            </Text>
            <Text variant="title" color="text" weight="bold" style={styles.nameText}>
              Anna 👋
            </Text>
          </View>
          <Avatar
            name="Anna"
            size="medium"
            borderColor="#FFFFFF"
          />
        </FadeInView>

        {/* Storage Quota */}
        <FadeInView delay={200} direction="up" style={styles.storageSection}>
          <Text style={[
            theme.typography.display.medium,
            { color: theme.colors.text, letterSpacing: -0.02 }
          ]}>
            {storageInfo.used}/{storageInfo.total} GB
          </Text>

          <Chip
            label="Add files"
            icon={IconNames.PLUS}
            onPress={handleAddFiles}
            variant="outline"
            style={[styles.addFilesButton, { backgroundColor: theme.colors.backgroundAlt }]}
          />
        </FadeInView>

        {/* Cloud Actions Row */}
        <FadeInView delay={300} direction="left" style={styles.cloudActions}>
          <IconButton
            icon={IconNames.PLUS}
            onPress={handleAddFiles}
            variant="secondary"
          />
          <IconButton
            icon={IconNames.CLOUD}
            onPress={() => {}}
            variant="secondary"
          />
          <IconButton
            icon={IconNames.CAMERA}
            onPress={() => {}}
            variant="secondary"
          />
          <IconButton
            icon={IconNames.HARD_DRIVE}
            onPress={() => {}}
            variant="secondary"
          />
        </FadeInView>

        {/* Cards Row */}
        <FadeInView delay={400} direction="up" style={styles.cardsRow}>
          {/* Statistics Card */}
          <GlassCard style={styles.statsCard} intensity={15} pressable onPress={onNavigateToStatistics}>
            <View style={styles.cardHeader}>
              <Text variant="body" color="text" weight="medium">
                Statistics
              </Text>
              <Text variant="caption" color="textSecondary">
                08/12/23
              </Text>
            </View>

            {/* Mini Pie Chart Placeholder */}
            <View style={styles.miniChart}>
              <View style={[styles.chartSegment, { backgroundColor: theme.colors.dataColors.pink }]} />
              <View style={[styles.chartSegment, { backgroundColor: theme.colors.dataColors.yellow }]} />
              <View style={[styles.chartSegment, { backgroundColor: theme.colors.dataColors.blue }]} />
              <View style={[styles.chartSegment, { backgroundColor: theme.colors.dataColors.purple }]} />
            </View>
          </GlassCard>

          {/* Shared With Card */}
          <GlassCard style={styles.sharedCard} intensity={15}>
            <View style={styles.cardHeader}>
              <Text variant="body" color="text" weight="medium">
                Shared with
              </Text>
            </View>

            <View style={styles.avatarStack}>
              <Avatar name="P1" size="small" />
              <Avatar name="P2" size="small" style={styles.stackedAvatar} />
              <Avatar name="P3" size="small" style={styles.stackedAvatar} />
            </View>
          </GlassCard>
        </FadeInView>

        {/* Recent Files */}
        <FadeInView delay={500} direction="up" style={styles.recentSection}>
          <View style={styles.sectionHeader}>
            <Text variant="title" color="text" weight="bold" style={styles.sectionTitle}>
              Recent files
            </Text>
            <AnimatedIcon
              name={IconNames.REFRESH}
              size={20}
              color={theme.colors.textSecondary}
              animation="rotate"
              duration={2000}
            />
          </View>

          <GlassCard style={styles.recentFilesCard} intensity={10}>
            {recentFiles.length > 0 ? (
              recentFiles.slice(0, 2).map((file, index) => {
                const fileTypeService = FileTypeService.getInstance();
                return (
                  <ListItem
                    key={file.path}
                    title={file.name}
                    subtitle={fileTypeService.formatDate(file.modifiedDate)}
                    leftIcon={file.mimeType?.startsWith('image/') ? IconNames.IMAGE : IconNames.FILE_TEXT}
                    rightIcon={IconNames.MORE}
                    accentColor={index === 0 ? theme.colors.dataColors.pink : theme.colors.dataColors.yellow}
                    onPress={() => handleFilePress(file)}
                  />
                );
              })
            ) : (
              <ListItem
                title="No recent files"
                subtitle="Start browsing files to see them here"
                leftIcon={IconNames.FOLDER}
                accentColor={theme.colors.dataColors.gray}
                onPress={handleAddFiles}
              />
            )}
          </GlassCard>
        </FadeInView>
      </ScrollView>

      {/* Floating Navigation */}
      <FloatingNav items={navItems} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 120,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 32,
  },
  headerLeft: {
    flex: 1,
  },
  welcomeText: {
    marginBottom: 4,
  },
  nameText: {
    fontSize: 20,
  },
  storageSection: {
    alignItems: 'center',
    marginBottom: 32,
  },
  addFilesButton: {
    marginTop: 16,
    borderWidth: 1.5,
    borderStyle: 'dashed',
  },
  cloudActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 32,
    paddingHorizontal: 20,
  },
  cardsRow: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 32,
  },
  statsCard: {
    flex: 1,
    minHeight: 120,
  },
  sharedCard: {
    flex: 1,
    minHeight: 120,
  },
  cardHeader: {
    marginBottom: 16,
  },
  miniChart: {
    flexDirection: 'row',
    height: 40,
    borderRadius: 20,
    overflow: 'hidden',
  },
  chartSegment: {
    flex: 1,
  },
  avatarStack: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  stackedAvatar: {
    marginLeft: -8,
  },
  recentSection: {
    marginBottom: 32,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    flex: 1,
  },
  recentFilesCard: {
    padding: 0,
  },
});
