import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  RefreshControl,
  Alert,
  Dimensions,
} from 'react-native';
import * as FileSystem from 'expo-file-system';
import { FileManagerScreenProps, FileItem } from '../types';
import { useTheme } from '../theme';
import { FileSystemService } from '../services/FileSystemService';
import { FileTypeService } from '../services/FileTypeService';
import { SearchService } from '../services/SearchService';
import { Text } from '../components/Text';
import { Button } from '../components/Button';
import { Input } from '../components/Input';
import { Icon, IconNames } from '../components/Icon';
import { FileListItem } from '../components/FileListItem';
import { FadeInView } from '../components/FadeInView';
import { ScaleOnPress } from '../components/ScaleOnPress';

const { width: screenWidth } = Dimensions.get('window');

export const FileManagerScreen: React.FC<FileManagerScreenProps> = ({
  initialPath,
  onFileSelect,
}) => {
  const { theme } = useTheme();
  const [currentPath, setCurrentPath] = useState(initialPath || FileSystem.documentDirectory || '');
  const [files, setFiles] = useState<FileItem[]>([]);
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [viewMode, setViewMode] = useState<'list' | 'grid' | 'detailed'>('list');
  const [sortBy, ] = useState<'name' | 'date' | 'size' | 'type'>('name');
  const [sortOrder, ] = useState<'asc' | 'desc'>('asc');
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearch, setShowSearch] = useState(false);
  const [navigationHistory, setNavigationHistory] = useState<string[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);

  const fileSystemService = FileSystemService.getInstance();
  const fileTypeService = FileTypeService.getInstance();
  const searchService = SearchService.getInstance();

  // Load directory contents
  const loadDirectory = useCallback(async (path: string, addToHistory = true) => {
    try {
      setLoading(true);
      const directoryFiles = await fileSystemService.listDirectory(path);
      const sortedFiles = fileTypeService.sortFiles(directoryFiles, sortBy, sortOrder);
      
      setFiles(sortedFiles);
      setCurrentPath(path);
      setSelectedFiles([]);

      // Update navigation history
      if (addToHistory) {
        const newHistory = navigationHistory.slice(0, historyIndex + 1);
        newHistory.push(path);
        setNavigationHistory(newHistory);
        setHistoryIndex(newHistory.length - 1);
      }

      // Index directory for search
      searchService.indexDirectory(path);
    } catch (error) {
      console.error('Failed to load directory:', error);
      Alert.alert('Error', 'Failed to load directory contents');
    } finally {
      setLoading(false);
    }
  }, [sortBy, sortOrder, navigationHistory, historyIndex, fileSystemService, searchService, fileTypeService]);

  // Initial load
  useEffect(() => {
    loadDirectory(currentPath, false);
  }, []);

  // Handle refresh
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadDirectory(currentPath, false);
    setRefreshing(false);
  }, [currentPath, loadDirectory]);

  // Handle file press
  const handleFilePress = useCallback((file: FileItem) => {
    if (file.type === 'directory') {
      loadDirectory(file.path);
    } else {
      if (onFileSelect) {
        onFileSelect(file);
      }
      // Add to recent files
      searchService.addToRecentFiles(file);
    }
  }, [loadDirectory, onFileSelect, searchService]);

  // Handle file long press
  const handleFileLongPress = useCallback((file: FileItem) => {
    setSelectedFiles(prev => {
      if (prev.includes(file.path)) {
        return prev.filter(path => path !== file.path);
      } else {
        return [...prev, file.path];
      }
    });
  }, []);

  // Handle back navigation
  const handleBack = useCallback(() => {
    if (historyIndex > 0) {
      const previousPath = navigationHistory[historyIndex - 1];
      setHistoryIndex(historyIndex - 1);
      loadDirectory(previousPath, false);
    }
  }, [historyIndex, navigationHistory, loadDirectory]);

  // Handle search
  const handleSearch = useCallback(async (query: string) => {
    if (!query.trim()) {
      await loadDirectory(currentPath, false);
      return;
    }

    try {
      setLoading(true);
      const searchResults = await searchService.searchFiles(query, currentPath);
      setFiles(searchResults);
    } catch (error) {
      console.error('Search failed:', error);
      Alert.alert('Error', 'Search failed');
    } finally {
      setLoading(false);
    }
  }, [currentPath, loadDirectory, searchService]);

  // Handle create folder
  const handleCreateFolder = useCallback(() => {
    Alert.prompt(
      'Create Folder',
      'Enter folder name:',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Create',
          onPress: async (folderName) => {
            if (folderName?.trim()) {
              try {
                await fileSystemService.createDirectory(currentPath, folderName.trim());
                await loadDirectory(currentPath, false);
              } catch (error) {
                Alert.alert('Error', 'Failed to create folder');
              }
            }
          },
        },
      ],
      'plain-text'
    );
  }, [currentPath, fileSystemService, loadDirectory]);

  // Handle delete selected files
  const handleDeleteSelected = useCallback(() => {
    if (selectedFiles.length === 0) return;

    Alert.alert(
      'Delete Files',
      `Are you sure you want to delete ${selectedFiles.length} item(s)?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await Promise.all(
                selectedFiles.map(path => fileSystemService.deleteItem(path))
              );
              await loadDirectory(currentPath, false);
            } catch (error) {
              Alert.alert('Error', 'Failed to delete some files');
            }
          },
        },
      ]
    );
  }, [selectedFiles, fileSystemService, currentPath, loadDirectory]);

  // Toggle view mode
  const toggleViewMode = useCallback(() => {
    const modes: Array<'list' | 'grid' | 'detailed'> = ['list', 'grid', 'detailed'];
    const currentIndex = modes.indexOf(viewMode);
    const nextIndex = (currentIndex + 1) % modes.length;
    setViewMode(modes[nextIndex]);
  }, [viewMode]);

  // Get grid columns
  const getNumColumns = () => {
    if (viewMode === 'grid') {
      return Math.floor(screenWidth / 120);
    }
    return 1;
  };

  // Render header
  const renderHeader = () => (
    <View style={[styles.header, { backgroundColor: theme.colors.surface }]}>
      <View style={styles.headerTop}>
        <View style={styles.navigationControls}>
          <ScaleOnPress onPress={handleBack} disabled={historyIndex <= 0}>
            <View style={[
              styles.navButton,
              {
                backgroundColor: historyIndex > 0 ? theme.colors.primary : theme.colors.border,
              }
            ]}>
              <Icon
                name={IconNames.BACK}
                size={20}
                color={historyIndex > 0 ? '#FFFFFF' : theme.colors.textSecondary}
              />
            </View>
          </ScaleOnPress>
        </View>

        <View style={styles.headerActions}>
          <ScaleOnPress onPress={() => setShowSearch(!showSearch)}>
            <View style={[styles.actionButton, { backgroundColor: theme.colors.primary }]}>
              <Icon name={IconNames.SEARCH} size={20} color="#FFFFFF" />
            </View>
          </ScaleOnPress>
          
          <ScaleOnPress onPress={toggleViewMode}>
            <View style={[styles.actionButton, { backgroundColor: theme.colors.secondary }]}>
              <Icon name="view" size={20} color="#FFFFFF" />
            </View>
          </ScaleOnPress>
        </View>
      </View>

      {showSearch && (
        <FadeInView direction="down" style={styles.searchContainer}>
          <Input
            placeholder="Search files..."
            value={searchQuery}
            onChangeText={(text) => {
              setSearchQuery(text);
              handleSearch(text);
            }}
            variant="filled"
          />
        </FadeInView>
      )}

      <View style={styles.pathContainer}>
        <Text variant="caption" color="textSecondary" numberOfLines={1}>
          {currentPath}
        </Text>
      </View>
    </View>
  );

  // Render empty state
  const renderEmptyState = () => (
    <FadeInView style={styles.emptyState}>
      <Icon name={IconNames.FOLDER} size={64} color={theme.colors.textSecondary} />
      <Text variant="body" color="textSecondary" style={styles.emptyText}>
        {searchQuery ? 'No files found' : 'This folder is empty'}
      </Text>
      {!searchQuery && (
        <Button
          title="Create Folder"
          onPress={handleCreateFolder}
          variant="outline"
          style={styles.createButton}
        />
      )}
    </FadeInView>
  );

  // Render floating action button
  const renderFAB = () => (
    <ScaleOnPress onPress={handleCreateFolder}>
      <View style={[styles.fab, { backgroundColor: theme.colors.primary }]}>
        <Icon name={IconNames.CREATE} size={24} color="#FFFFFF" />
      </View>
    </ScaleOnPress>
  );

  // Render selection toolbar
  const renderSelectionToolbar = () => {
    if (selectedFiles.length === 0) return null;

    return (
      <FadeInView direction="up" style={[styles.selectionToolbar, { backgroundColor: theme.colors.primary }]}>
        <Text variant="body" color="text" weight="medium" style={{ color: '#FFFFFF' }}>
          {selectedFiles.length} selected
        </Text>
        <View style={styles.toolbarActions}>
          <ScaleOnPress onPress={handleDeleteSelected}>
            <View style={styles.toolbarButton}>
              <Icon name={IconNames.DELETE} size={20} color="#FFFFFF" />
            </View>
          </ScaleOnPress>
          <ScaleOnPress onPress={() => setSelectedFiles([])}>
            <View style={styles.toolbarButton}>
              <Icon name="close" size={20} color="#FFFFFF" />
            </View>
          </ScaleOnPress>
        </View>
      </FadeInView>
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {renderHeader()}
      
      <FlatList
        data={files}
        keyExtractor={(item) => item.path}
        renderItem={({ item }) => (
          <FileListItem
            item={item}
            viewMode={viewMode}
            onPress={() => handleFilePress(item)}
            onLongPress={() => handleFileLongPress(item)}
            isSelected={selectedFiles.includes(item.path)}
          />
        )}
        numColumns={getNumColumns()}
        key={`${viewMode}-${getNumColumns()}`}
        contentContainerStyle={files.length === 0 ? styles.emptyContainer : undefined}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor={theme.colors.primary}
          />
        }
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
      />

      {renderFAB()}
      {renderSelectionToolbar()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 50,
    paddingBottom: 16,
    paddingHorizontal: 16,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  navigationControls: {
    flexDirection: 'row',
  },
  navButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  searchContainer: {
    marginBottom: 12,
  },
  pathContainer: {
    paddingHorizontal: 4,
  },
  emptyContainer: {
    flex: 1,
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 32,
  },
  emptyText: {
    marginTop: 16,
    marginBottom: 24,
    textAlign: 'center',
  },
  createButton: {
    minWidth: 120,
  },
  fab: {
    position: 'absolute',
    bottom: 80,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  selectionToolbar: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    paddingBottom: 32,
  },
  toolbarActions: {
    flexDirection: 'row',
    gap: 16,
  },
  toolbarButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
});