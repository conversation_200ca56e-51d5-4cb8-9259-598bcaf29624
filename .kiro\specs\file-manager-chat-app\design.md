# Design Document

## Overview

The File Manager Chat App is a React Native application that combines comprehensive file management capabilities with an integrated chat interface. The app emphasizes modern UI/UX principles using shadcn-inspired components, smooth animations, and microinteractions. The architecture is designed to be modular and extensible, preparing for future AI automation features while maintaining excellent performance and user experience.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    A[React Native App] --> B[Navigation Layer]
    A --> C[State Management - Zustand]
    A --> D[UI Components - shadcn/rn]
    
    B --> E[File Browser Screen]
    B --> F[Chat Screen]
    B --> G[Settings Screen]
    
    E --> H[File System Service]
    E --> I[File Operations Service]
    E --> J[Search Service]
    
    F --> K[Chat Service]
    F --> L[Message Storage]
    
    H --> M[React Native FS]
    I --> N[Device File System]
    J --> O[Local Search Index]
    
    D --> P[Animation System - Reanimated]
    D --> Q[Gesture Handler]
    D --> R[Theme Provider]
```

### Technology Stack

- **Framework**: React Native 0.73+
- **Language**: TypeScript
- **Navigation**: React Navigation 6
- **State Management**: Zustand
- **UI Components**: Custom shadcn-inspired components
- **Animations**: React Native Reanimated 3
- **Gestures**: React Native Gesture Handler
- **File System**: react-native-fs
- **Icons**: React Native Vector Icons / Lucide React Native
- **Storage**: AsyncStorage for preferences, SQLite for chat history

## Components and Interfaces

### Core Components

#### 1. FileManagerScreen
```typescript
interface FileManagerScreenProps {
  initialPath?: string;
  onFileSelect?: (file: FileItem) => void;
}

interface FileItem {
  name: string;
  path: string;
  type: 'file' | 'directory';
  size: number;
  modifiedDate: Date;
  mimeType?: string;
  thumbnail?: string;
}
```

#### 2. ChatInterface
```typescript
interface ChatInterfaceProps {
  isVisible: boolean;
  onClose: () => void;
  messages: ChatMessage[];
  onSendMessage: (message: string) => void;
}

interface ChatMessage {
  id: string;
  text: string;
  timestamp: Date;
  sender: 'user' | 'assistant';
  type: 'text' | 'file_reference';
}
```

#### 3. FileListItem
```typescript
interface FileListItemProps {
  item: FileItem;
  viewMode: 'list' | 'grid' | 'detailed';
  onPress: () => void;
  onLongPress: () => void;
  isSelected: boolean;
}
```

#### 4. AnimatedComponents
```typescript
// Custom animated components with shadcn styling
- AnimatedButton
- AnimatedCard
- AnimatedModal
- SlideUpPanel
- FadeInView
- ScaleOnPress
```

### Services Architecture

#### FileSystemService
```typescript
class FileSystemService {
  async listDirectory(path: string): Promise<FileItem[]>
  async createDirectory(path: string, name: string): Promise<void>
  async deleteItem(path: string): Promise<void>
  async copyItem(source: string, destination: string): Promise<void>
  async moveItem(source: string, destination: string): Promise<void>
  async renameItem(path: string, newName: string): Promise<void>
  async getFileInfo(path: string): Promise<FileItem>
  async generateThumbnail(imagePath: string): Promise<string>
}
```

#### SearchService
```typescript
class SearchService {
  async indexDirectory(path: string): Promise<void>
  async searchFiles(query: string, path?: string): Promise<FileItem[]>
  async getRecentFiles(limit: number): Promise<FileItem[]>
  async addToRecentFiles(file: FileItem): Promise<void>
}
```

#### ChatService
```typescript
class ChatService {
  async sendMessage(message: string): Promise<ChatMessage>
  async getMessageHistory(): Promise<ChatMessage[]>
  async saveMessage(message: ChatMessage): Promise<void>
  async clearHistory(): Promise<void>
}
```

## Data Models

### File System Models
```typescript
interface FileItem {
  name: string;
  path: string;
  type: 'file' | 'directory';
  size: number;
  modifiedDate: Date;
  mimeType?: string;
  thumbnail?: string;
  isHidden: boolean;
  permissions: FilePermissions;
}

interface FilePermissions {
  readable: boolean;
  writable: boolean;
  executable: boolean;
}

interface DirectoryContents {
  path: string;
  items: FileItem[];
  totalSize: number;
  itemCount: number;
}
```

### Chat Models
```typescript
interface ChatMessage {
  id: string;
  text: string;
  timestamp: Date;
  sender: 'user' | 'assistant';
  type: 'text' | 'file_reference' | 'system';
  metadata?: {
    fileReference?: string;
    actionType?: string;
  };
}

interface ChatSession {
  id: string;
  messages: ChatMessage[];
  createdAt: Date;
  lastActivity: Date;
}
```

### UI State Models
```typescript
interface AppState {
  currentPath: string;
  selectedFiles: string[];
  viewMode: 'list' | 'grid' | 'detailed';
  sortBy: 'name' | 'date' | 'size' | 'type';
  sortOrder: 'asc' | 'desc';
  showHiddenFiles: boolean;
  theme: 'light' | 'dark';
  chatVisible: boolean;
}

interface NavigationState {
  history: string[];
  currentIndex: number;
  canGoBack: boolean;
  canGoForward: boolean;
}
```

## Error Handling

### Error Types
```typescript
enum FileManagerError {
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  FILE_NOT_FOUND = 'FILE_NOT_FOUND',
  STORAGE_FULL = 'STORAGE_FULL',
  NETWORK_ERROR = 'NETWORK_ERROR',
  INVALID_OPERATION = 'INVALID_OPERATION'
}

interface ErrorState {
  type: FileManagerError;
  message: string;
  recoverable: boolean;
  retryAction?: () => void;
}
```

### Error Handling Strategy
1. **Graceful Degradation**: App continues to function with reduced capabilities
2. **User-Friendly Messages**: Clear, actionable error messages
3. **Retry Mechanisms**: Automatic retry for transient errors
4. **Offline Support**: Basic file operations work without network
5. **Error Reporting**: Optional crash reporting for debugging

## Testing Strategy

### Unit Testing
- **Services**: FileSystemService, SearchService, ChatService
- **Utilities**: File type detection, path manipulation, formatting
- **State Management**: Zustand stores and actions
- **Components**: Individual component logic and props handling

### Integration Testing
- **File Operations**: End-to-end file management workflows
- **Navigation**: Screen transitions and state persistence
- **Search**: Search functionality across different file types
- **Chat Integration**: Message flow and storage

### Performance Testing
- **Large Directory Handling**: 1000+ files performance
- **Memory Usage**: Thumbnail generation and caching
- **Animation Performance**: 60fps maintenance during interactions
- **Startup Time**: App launch and initial directory loading

### UI Testing
- **Accessibility**: Screen reader support and keyboard navigation
- **Responsive Design**: Different screen sizes and orientations
- **Theme Switching**: Light/dark mode transitions
- **Gesture Recognition**: Touch interactions and long presses

## Animation and Interaction Design

### Animation Principles
1. **Purposeful Motion**: Every animation serves a functional purpose
2. **Natural Timing**: Easing curves that feel organic (spring animations)
3. **Spatial Awareness**: Elements move in logical directions
4. **Feedback**: Visual confirmation of user actions
5. **Performance**: 60fps target with efficient animations

### Key Animations
```typescript
// File list animations
const listItemAnimation = {
  entering: FadeInDown.duration(300).springify(),
  exiting: FadeOutUp.duration(200),
  layout: LinearTransition.springify()
};

// Chat panel animation
const chatPanelAnimation = {
  entering: SlideInUp.duration(400).springify(),
  exiting: SlideOutDown.duration(300)
};

// Button interactions
const buttonAnimation = {
  onPress: withSpring(0.95),
  onRelease: withSpring(1.0)
};
```

### Microinteractions
- **File Selection**: Subtle scale and color change
- **Long Press**: Haptic feedback with visual indication
- **Swipe Actions**: Reveal actions with smooth transitions
- **Loading States**: Skeleton screens with shimmer effects
- **Success/Error**: Toast notifications with appropriate icons

## Theme and Styling

### Design System
```typescript
const theme = {
  colors: {
    primary: '#0066CC',
    secondary: '#6B7280',
    background: '#FFFFFF',
    surface: '#F9FAFB',
    text: '#111827',
    textSecondary: '#6B7280',
    border: '#E5E7EB',
    success: '#10B981',
    warning: '#F59E0B',
    error: '#EF4444'
  },
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32
  },
  typography: {
    heading: {
      fontSize: 24,
      fontWeight: '600',
      lineHeight: 32
    },
    body: {
      fontSize: 16,
      fontWeight: '400',
      lineHeight: 24
    },
    caption: {
      fontSize: 14,
      fontWeight: '400',
      lineHeight: 20
    }
  },
  borderRadius: {
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16
  }
};
```

### Component Styling
- **Consistent Spacing**: 8px grid system
- **Elevation**: Subtle shadows for depth
- **Typography**: Clear hierarchy with appropriate weights
- **Color Usage**: Semantic color system with accessibility compliance
- **Responsive**: Adapts to different screen sizes and orientations