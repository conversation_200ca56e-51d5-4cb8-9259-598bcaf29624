import React, { useEffect } from 'react';
import { ViewStyle } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withRepeat,
  withTiming,
  interpolate,
  Easing,
} from 'react-native-reanimated';
import { Icon } from './Icon';

interface AnimatedIconProps {
  name: string;
  size?: number;
  color?: string;
  style?: ViewStyle;
  animation?: 'none' | 'bounce' | 'pulse' | 'rotate' | 'shake' | 'float';
  duration?: number;
  delay?: number;
}

export const AnimatedIcon: React.FC<AnimatedIconProps> = ({
  name,
  size = 24,
  color,
  style,
  animation = 'none',
  duration = 1000,
  delay = 0,
}) => {
  const animationValue = useSharedValue(0);
  const scale = useSharedValue(1);
  const rotation = useSharedValue(0);
  const translateY = useSharedValue(0);
  const translateX = useSharedValue(0);

  useEffect(() => {
    const startAnimation = () => {
      switch (animation) {
        case 'bounce':
          scale.value = withRepeat(
            withSpring(1.2, { damping: 2, stiffness: 100 }),
            -1,
            true
          );
          break;
        case 'pulse':
          animationValue.value = withRepeat(
            withTiming(1, { duration, easing: Easing.inOut(Easing.ease) }),
            -1,
            true
          );
          break;
        case 'rotate':
          rotation.value = withRepeat(
            withTiming(360, { duration, easing: Easing.linear }),
            -1,
            false
          );
          break;
        case 'shake':
          translateX.value = withRepeat(
            withTiming(5, { duration: 100, easing: Easing.linear }),
            6,
            true
          );
          break;
        case 'float':
          translateY.value = withRepeat(
            withTiming(-5, { duration: duration / 2, easing: Easing.inOut(Easing.ease) }),
            -1,
            true
          );
          break;
        default:
          break;
      }
    };

    if (animation !== 'none') {
      const timer = setTimeout(startAnimation, delay);
      return () => clearTimeout(timer);
    }
  }, [animation, duration, delay, animationValue, scale, rotation, translateY, translateX]);

  const animatedStyle = useAnimatedStyle(() => {
    let animatedScale = scale.value;
    let animatedRotation = rotation.value;
    let animatedTranslateY = translateY.value;
    let animatedTranslateX = translateX.value;

    if (animation === 'pulse') {
      animatedScale = interpolate(animationValue.value, [0, 1], [1, 1.1]);
    }

    return {
      transform: [
        { scale: animatedScale },
        { rotate: `${animatedRotation}deg` },
        { translateY: animatedTranslateY },
        { translateX: animatedTranslateX },
      ],
    };
  });

  return (
    <Animated.View style={[animatedStyle, style]}>
      <Icon name={name} size={size} color={color} />
    </Animated.View>
  );
};
