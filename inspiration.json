{
  "meta": {
    "version": "1.0",
    "generated_at": "2025-08-08T18:22:00+05:30",
    "purpose": "UI replication spec for mobile app screens shown in reference image",
    "notes": "This JSON is a comprehensive, implementation-oriented spec for designers, engineers, and an AI agent to recreate the design precisely. Dimensions use points (pt) and percentages relative to device width/height. Colors are in HEX with opacity as rgba where needed. Typography follows semantic tokens and can be mapped to platform fonts. Shadows, radii, spacings, and component variants are included."
  },
  "foundations": {
    "grid": {
      "base_unit": 4,
      "spacing_scale": [0,2,4,6,8,12,16,20,24,28],
      "container_padding": 16,
      "corner_radius_scale": {
        "xs": 6,
        "sm": 10,
        "md": 16,
        "lg": 20,
        "xl": 28,
        "pill": 999
      }
    },
    "elevation": {
      "x1": { "blur": 8, "y": 2, "spread": 0, "color": "rgba(0,0,0,0.06)" },
      "x2": { "blur": 16, "y": 6, "spread": 0, "color": "rgba(0,0,0,0.10)" },
      "x3": { "blur": 28, "y": 12, "spread": -4, "color": "rgba(0,0,0,0.16)" }
    },
    "typography": {
      "font_family": {
        "sans_primary": "Inter, SF Pro, Roboto",
        "sans_alt": "Manrope, Poppins"
      },
      "scale": {
        "display_l": { "size": 34, "line": 40, "weight": 700, "tracking": -0.5 },
        "display_m": { "size": 28, "line": 34, "weight": 700, "tracking": -0.4 },
        "title_l":   { "size": 22, "line": 28, "weight": 700, "tracking": -0.2 },
        "title_m":   { "size": 18, "line": 24, "weight": 700 },
        "title_s":   { "size": 16, "line": 22, "weight": 600 },
        "body_l":    { "size": 16, "line": 24, "weight": 500 },
        "body_m":    { "size": 14, "line": 20, "weight": 500 },
        "body_s":    { "size": 12, "line": 18, "weight": 500 },
        "label":     { "size": 12, "line": 16, "weight": 600 },
        "caption":   { "size": 11, "line": 14, "weight": 500 }
      }
    },
    "colors": {
      "light": {
        "bg": "#FFFFFF",
        "bg_alt": "#F7F7FB",
        "card": "#FFFFFF",
        "text_primary": "#0F1115",
        "text_secondary": "#6B7280",
        "text_tertiary": "#98A2B3",
        "border": "#ECECF3",
        "accent_pink": "#F8C2D9",
        "accent_purple": "#C9A6FF",
        "accent_blue": "#7BC8FF",
        "accent_yellow": "#FFE06E",
        "accent_green": "#9BE7C4",
        "brand": "#FF77B7"
      },
      "dark": {
        "bg": "#0F1115",
        "bg_alt": "#161A1F",
        "card": "#12161C",
        "text_primary": "#F5F7FA",
        "text_secondary": "#C7CDD8",
        "text_tertiary": "#9AA3B2",
        "border": "#222833",
        "accent_pink": "#FF73C5",
        "accent_purple": "#B097FF",
        "accent_blue": "#78C6FF",
        "accent_yellow": "#FFD76A",
        "accent_green": "#8FE4BE",
        "brand": "#FF77B7"
      },
      "data": {
        "pictures": "#FF8AD6",
        "documents": "#FFE06E",
        "videos": "#7BC8FF",
        "others": "#9BE7C4",
        "progress_bg": "rgba(255,255,255,0.08)",
        "progress_light_bg": "rgba(15,17,21,0.06)"
      }
    }
  },
  "components": {
    "avatar": {
      "size": 36,
      "shape": "circle",
      "border": { "width": 2, "color_light": "#FFFFFF", "color_dark": "#12161C" }
    },
    "icon_button": {
      "size": 44,
      "radius": 14,
      "background_light": "#FFFFFF",
      "background_dark": "#161A1F",
      "shadow": "x1",
      "icon_size": 22
    },
    "chip": {
      "height": 32,
      "radius": 14,
      "padding_h": 12,
      "typography": "body_s",
      "bg_light": "#F7F7FB",
      "bg_dark": "#161A1F",
      "icon_size": 16
    },
    "card": {
      "radius": 20,
      "padding": 16,
      "shadow": "x1",
      "bg_light": "#FFFFFF",
      "bg_dark": "#12161C"
    },
    "progress_ring": {
      "thickness": 12,
      "cap": "round",
      "trail_color_light": "rgba(15,17,21,0.08)",
      "trail_color_dark": "rgba(255,255,255,0.08)",
      "glow": { "blur": 12, "color": "rgba(255,119,183,0.35)" }
    },
    "list_item": {
      "height": 64,
      "thumb_size": 36,
      "radius": 14,
      "bg_light": "#FFFFFF",
      "bg_dark": "#12161C",
      "divider_light": "#ECECF3",
      "divider_dark": "#222833"
    },
    "floating_nav": {
      "height": 44,
      "radius": 16,
      "bg": "rgba(15,17,21,0.88)",
      "icon": { "size": 18, "color": "#FFFFFF" }
    }
  },
  "screens": [
    {
      "id": "home_light",
      "theme": "light",
      "device": { "width": 390, "height": 844, "safe_top": 48, "safe_bottom": 34 },
      "background": { "type": "solid", "color": "#FFFFFF" },
      "gradient_overlay": { "enabled": true, "colors": ["#FCE7F3","#EDE9FE"], "opacity": 0.18, "angle": 90 },
      "layout": [
        {
          "type": "app_header",
          "frame": { "x": 16, "y": 60, "w": 358, "h": 44 },
          "avatar": { "src": "anna.jpg", "position": "right" },
          "title": { "text": "Welcome Back", "style": "body_m", "color": "text_secondary" },
          "subtitle": { "text": "Anna 👋", "style": "title_s", "color": "text_primary" }
        },
        {
          "type": "storage_quota",
          "frame": { "x": 16, "y": 116, "w": 358, "h": 72 },
          "used_text": { "text": "432/860 GB", "style": "display_m", "color": "text_primary", "tracking": -0.5 },
          "add_files_button": {
            "label": "Add files",
            "variant": "ghost",
            "left_icon": "upload",
            "frame": { "x": 16, "y": 188, "w": 112, "h": 36 },
            "radius": 14,
            "bg": "#F7F7FB",
            "icon_color": "#FF77B7"
          }
        },
        {
          "type": "cloud_actions",
          "frame": { "x": 16, "y": 232, "w": 358, "h": 50 },
          "buttons": [
            { "icon": "plus", "bg": "#FFFFFF", "shadow": "x1" },
            { "icon": "icloud", "bg": "#FFFFFF", "shadow": "x1" },
            { "icon": "dropbox", "bg": "#FFFFFF", "shadow": "x1" },
            { "icon": "google_drive", "bg": "#FFFFFF", "shadow": "x1" }
          ],
          "gap": 12,
          "radius": 14
        },
        {
          "type": "cards_row",
          "frame": { "x": 16, "y": 292, "w": 358, "h": 160 },
          "gap": 12,
          "cards": [
            {
              "id": "stats_card",
              "title": "Statistics",
              "subtitle": "08/12/23",
              "style": "card",
              "chart": {
                "type": "pie_min",
                "segments": [
                  { "label": "Pictures", "value": 48, "color": "data.pictures" },
                  { "label": "Docs", "value": 28, "color": "data.documents" },
                  { "label": "Videos", "value": 16, "color": "data.videos" },
                  { "label": "Other", "value": 8, "color": "data.others" }
                ]
              }
            },
            {
              "id": "shared_with",
              "title": "Shared with",
              "style": "card",
              "avatars": [
                { "src": "p1.jpg" },
                { "src": "p2.jpg" },
                { "src": "p3.jpg" }
              ],
              "accent_bg": "#FFE8F5"
            }
          ]
        },
        {
          "type": "recent_files_list",
          "frame": { "x": 16, "y": 464, "w": 358, "h": 260 },
          "title": { "text": "Recent files", "style": "title_s" },
          "items": [
            {
              "icon": "image",
              "title": "IMG2075.jpg",
              "meta": "08/12/2023 · 3:38 AM",
              "accent": "#F8C2D9",
              "actions": ["more"]
            },
            {
              "icon": "doc",
              "title": "Permission.doc",
              "meta": "08/12/2023 · 3:38 AM",
              "accent": "#FFE06E",
              "actions": ["more"]
            }
          ],
          "divider": true
        },
        {
          "type": "floating_nav",
          "frame": { "x": 98, "y": 756, "w": 194, "h": 44 },
          "items": [
            { "icon": "home_filled", "active": true },
            { "icon": "search" },
            { "icon": "gear" }
          ]
        }
      ]
    },
    {
      "id": "statistics_dark",
      "theme": "dark",
      "device": { "width": 390, "height": 844, "safe_top": 48, "safe_bottom": 34 },
      "background": { "type": "solid", "color": "#0F1115" },
      "layout": [
        {
          "type": "promo_banner",
          "frame": { "x": 16, "y": 60, "w": 358, "h": 64 },
          "title": "Optimize your space!",
          "subtitle": "See our tutorial.",
          "cta_icon": "play",
          "bg": "#161A1F",
          "radius": 18,
          "illustration": "bot_orb.png"
        },
        {
          "type": "screen_title",
          "frame": { "x": 16, "y": 136, "w": 358, "h": 28 },
          "title": { "text": "Statistics", "style": "title_l", "color": "text_primary" },
          "subtitle": { "text": "08/12/23", "style": "body_s", "color": "text_tertiary" },
          "right_badge": { "text": "76% Used", "icon": "trend_up", "bg": "rgba(255,255,255,0.06)", "radius": 12 }
        },
        {
          "type": "radial_gauge_card",
          "frame": { "x": 16, "y": 180, "w": 358, "h": 300 },
          "bg": "#12161C",
          "radius": 24,
          "shadow": "x2",
          "gauge": {
            "start_angle": -210,
            "end_angle": 30,
            "rings": [
              { "label": "Total", "value": 432, "total": 860, "color": "#FF77B7", "thickness": 16, "glow": true },
              { "label": "Pictures", "value": 219, "total": 860, "color": "#FF8AD6" },
              { "label": "Documents", "value": 165, "total": 860, "color": "#FFE06E" },
              { "label": "Videos", "value": 48, "total": 860, "color": "#7BC8FF" }
            ],
            "center_label": {
              "lines": [
                { "text": "432", "style": "display_l", "color": "text_primary" },
                { "text": "GB", "style": "body_m", "color": "text_secondary" }
              ]
            }
          },
          "legend": {
            "items": [
              { "icon": "image", "title": "Pictures", "files": 1685, "size_gb": 219, "color": "#FF8AD6" },
              { "icon": "doc", "title": "Docs", "files": 1160, "size_gb": 165, "color": "#FFE06E" }
            ]
          }
        }
      ]
    },
    {
      "id": "folder_dark",
      "theme": "dark",
      "device": { "width": 390, "height": 844, "safe_top": 48, "safe_bottom": 34 },
      "background": { "type": "solid", "color": "#0F1115" },
      "layout": [
        {
          "type": "nav_bar",
          "frame": { "x": 0, "y": 52, "w": 390, "h": 44 },
          "left_icon": "chevron_left",
          "title": null,
          "bg": "transparent"
        },
        {
          "type": "folder_header",
          "frame": { "x": 16, "y": 96, "w": 358, "h": 120 },
          "title": { "text": "My Folders", "style": "body_m", "color": "text_tertiary" },
          "subtitle": { "text": "Compositions Tutorial", "style": "display_m", "color": "text_primary" },
          "add_files_button": {
            "label": "Add files",
            "variant": "dashed",
            "icon": "upload",
            "stroke": { "color": "rgba(255,255,255,0.24)", "dash":  },
            "radius": 14
          }
        },
        {
          "type": "hero_preview",
          "frame": { "x": 46, "y": 200, "w": 298, "h": 220 },
          "bg": "#161A1F",
          "radius": 22,
          "image": "toy_cluster.png",
          "inset_padding": 18
        },
        {
          "type": "files_list",
          "frame": { "x": 16, "y": 436, "w": 358, "h": 328 },
          "items": [
            { "thumb": "image", "title": "IMG2075.jpg", "meta": "08/12/2023 · 3:38 AM", "menu": true, "selected": false },
            { "thumb": "image", "title": "IMG2074.jpg", "meta": "08/12/2023 · 3:38 AM", "menu": true, "selected": true, "badge": { "type": "preview", "color": "#FF8AD6" } },
            { "thumb": "image", "title": "IMG2073.jpg", "meta": "08/12/2023 · 3:38 AM", "menu": true, "selected": false }
          ],
          "divider": { "color": "#222833" }
        }
      ]
    }
  ],
  "interactions": {
    "gestures": [
      { "target": "files_list.items[*]", "gesture": "swipe_left", "reveal_actions": ["share","delete","more"] },
      { "target": "floating_nav.items", "gesture": "tap", "navigate_to": "statistics_dark" },[3]
      { "target": "recent_files_list.items", "gesture": "tap", "navigate_to": "folder_dark" }
    ],
    "animations": {
      "screen_transition": { "type": "spring", "duration_ms": 420, "damping": 20, "stiffness": 160 },
      "gauge_sweep": { "type": "easeOut", "duration_ms": 1200, "delay_ms": 150 },
      "button_press": { "type": "scale", "from": 1.0, "to": 0.96, "duration_ms": 90 }
    },
    "haptics": {
      "tap": "light",
      "action": "medium",
      "success": "success"
    }
  },
  "accessibility": {
    "min_contrast": 4.5,
    "hit_target_min": 44,
    "dynamic_type": {
      "enabled": true,
      "steps": { "body_m": , "title_m": , "display_m":  }
    },
    "voice_labels": {
      "add_files_button": "Add files",
      "storage_quota": "Used 432 of 860 gigabytes",
      "statistics_nav": "Open Statistics",
      "back": "Go back"
    }
  },
  "assets": {
    "icons": [
      "upload","plus","icloud","dropbox","google_drive","image","doc","play","trend_up","chevron_left","search","gear","home_filled","more"
    ],
    "images": [
      "anna.jpg",
      "bot_orb.png",
      "toy_cluster.png",
      "p1.jpg","p2.jpg","p3.jpg"
    ]
  },
  "theme_support": {
    "supports_light_dark": true,
    "switch_strategy": "system",
    "blend_tints": {
      "light_surface_tint": "rgba(255,119,183,0.06)",
      "dark_surface_tint": "rgba(255,119,183,0.08)"
    }
  },
  "data_bindings": {
    "quota": { "used_gb": 432, "total_gb": 860 },
    "date_format": "MM/DD/YYYY · h:mm A",
    "lists": {
      "recent_files": "recent_files_list.items",
      "folder_files": "files_list.items"
    }
  },
  "dev_notes": {
    "platforms": [
      "React Native + Expo",
      "Flutter",
      "SwiftUI",
      "Jetpack Compose"
    ],
    "implementation_hints": [
      "Use a shared DesignToken provider to map foundations.colors and foundations.typography to your theming system.",
      "For radial gauge, render arcs with start/end angles and round line caps; apply a soft outer glow using shadow layers.",
      "Apply backdrop/gradient overlay on light home screen for subtle pink-lilac wash.",
      "Use elevation x2 for big cards on dark to achieve depth without harsh borders.",
      "Dashed Add files button uses a 1.5pt stroke with 6px dash/6px gap and 14px radius."
    ]
  },
  "_comments": [
    "All frames assume iPhone 13/14 (390x844pt). Adjust with container padding and percentages for other devices.",
    "Accent colors match the screenshot mood; tweak to brand as needed.",
    "If using a design agent, respect z-order: headers > hero > cards > lists > floating nav.",
    "Icons can be pulled from Lucide/Feather; keep rounded stroke 1.75pt for consistency.",
    "Typography weights rely on variable fonts if available; fall back to 600 where 700 looks too bold.",
    "Set list selected item state with subtle 4% brand tint background in dark."
  ]
}
