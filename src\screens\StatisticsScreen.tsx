import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import Svg, { Circle, Path } from 'react-native-svg';
import { useTheme } from '../theme';
import { Text } from '../components/Text';
import { Icon } from '../components/Icon';
import { FadeInView } from '../components/FadeInView';
import { DeviceFileSystemService } from '../services/DeviceFileSystemService';

// const { width: screenWidth } = Dimensions.get('window');

interface StatisticsScreenProps {
  onBack?: () => void;
}

export const StatisticsScreen: React.FC<StatisticsScreenProps> = ({ onBack }) => {
  const { theme } = useTheme();
  const [storageData, setStorageData] = useState({
    total: 432,
    pictures: 219,
    documents: 165,
    videos: 48,
    picturesFiles: 1685,
    documentsFiles: 1160,
  });

  useEffect(() => {
    loadStorageData();
  }, []);

  const loadStorageData = async () => {
    try {
      const deviceService = DeviceFileSystemService.getInstance();
      const info = await deviceService.getStorageInfo();
      // Convert to GB and set mock data for demo
      setStorageData({
        total: Math.round(info.usedSpace / (1024 * 1024 * 1024)),
        pictures: 219,
        documents: 165,
        videos: 48,
        picturesFiles: 1685,
        documentsFiles: 1160,
      });
    } catch (error) {
      console.error('Failed to load storage data:', error);
    }
  };

  const RadialGauge = () => {
    const centerX = 179;
    const centerY = 150;
    const radius = 120;
    const strokeWidth = 16;
    
    // Calculate angles for each segment
    const totalValue = 860;
    const picturesAngle = (storageData.pictures / totalValue) * 360;
    const documentsAngle = (storageData.documents / totalValue) * 360;
    const videosAngle = (storageData.videos / totalValue) * 360;
    
    const createArc = (startAngle: number, endAngle: number, color: string, isMain = false) => {
      const start = (startAngle - 90) * (Math.PI / 180);
      const end = (endAngle - 90) * (Math.PI / 180);
      
      const x1 = centerX + radius * Math.cos(start);
      const y1 = centerY + radius * Math.sin(start);
      const x2 = centerX + radius * Math.cos(end);
      const y2 = centerY + radius * Math.sin(end);
      
      const largeArcFlag = endAngle - startAngle <= 180 ? "0" : "1";
      
      return (
        <Path
          key={`${startAngle}-${endAngle}`}
          d={`M ${x1} ${y1} A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`}
          stroke={color}
          strokeWidth={isMain ? strokeWidth : strokeWidth - 4}
          strokeLinecap="round"
          fill="none"
        />
      );
    };

    return (
      <View style={styles.gaugeContainer}>
        <Svg width={358} height={300} style={styles.gaugeSvg}>
          {/* Background circle */}
          <Circle
            cx={centerX}
            cy={centerY}
            r={radius}
            stroke="rgba(255, 255, 255, 0.08)"
            strokeWidth={strokeWidth}
            fill="none"
          />
          
          {/* Main progress arc */}
          {createArc(-210, 30, '#FF77B7', true)}
          
          {/* Individual segments */}
          {createArc(-210, -210 + picturesAngle, '#FF8AD6')}
          {createArc(-210 + picturesAngle, -210 + picturesAngle + documentsAngle, '#FFE06E')}
          {createArc(-210 + picturesAngle + documentsAngle, -210 + picturesAngle + documentsAngle + videosAngle, '#7BC8FF')}
        </Svg>
        
        {/* Center label */}
        <View style={styles.centerLabel}>
          <Text style={[styles.centerValue, { color: theme.colors.text }]}>
            {storageData.total}
          </Text>
          <Text style={[styles.centerUnit, { color: theme.colors.textSecondary }]}>
            GB
          </Text>
        </View>
      </View>
    );
  };

  const LegendItem = ({ 
    iconName, 
    title, 
    files, 
    sizeGb, 
    color 
  }: { 
    iconName: string; 
    title: string; 
    files: number; 
    sizeGb: number; 
    color: string;
  }) => (
    <View style={styles.legendItem}>
      <View style={[styles.legendIcon, { backgroundColor: color }]}>
        <Icon name={iconName} size={20} color="#FFFFFF" />
      </View>
      <View style={styles.legendContent}>
        <View style={styles.legendHeader}>
          <Text variant="body" weight="medium" color="text">
            {title}
          </Text>
          <Text variant="caption" color="textSecondary">
            {files} Files
          </Text>
        </View>
        <Text variant="body" weight="semibold" color="text">
          {sizeGb} GB
        </Text>
      </View>
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Navigation Bar */}
        <FadeInView style={styles.navBar}>
          <TouchableOpacity style={styles.backButton} onPress={onBack}>
            <Icon name="chevron-left" size={24} color={theme.colors.text} />
          </TouchableOpacity>
        </FadeInView>

        {/* Promo Banner */}
        <FadeInView style={styles.promoBanner} delay={100}>
          <View style={[styles.bannerContent, { backgroundColor: theme.colors.surface }]}>
            <View style={styles.bannerText}>
              <Text variant="body" weight="semibold" color="text">
                Optimize your space!
              </Text>
              <Text variant="caption" color="textSecondary">
                See our tutorial.
              </Text>
            </View>
            <TouchableOpacity style={styles.playButton}>
              <Icon name="play" size={16} color={theme.colors.text} />
            </TouchableOpacity>
            <View style={styles.botOrb}>
              <View style={styles.orbGradient} />
            </View>
          </View>
        </FadeInView>

        {/* Screen Title */}
        <FadeInView style={styles.titleSection} delay={200}>
          <View style={styles.titleHeader}>
            <View>
              <Text variant="heading" color="text">
                Statistics
              </Text>
              <Text variant="caption" color="textSecondary">
                08/12/23
              </Text>
            </View>
            <View style={styles.usageBadge}>
              <Icon name="trending-up" size={12} color={theme.colors.text} />
              <Text variant="caption" weight="medium" color="text" style={styles.usageText}>
                76% Used
              </Text>
            </View>
          </View>
        </FadeInView>

        {/* Radial Gauge Card */}
        <FadeInView style={styles.gaugeCard} delay={300}>
          <View style={[styles.cardContainer, { backgroundColor: theme.colors.card }]}>
            <RadialGauge />
            
            {/* Legend */}
            <View style={styles.legend}>
              <LegendItem
                iconName="image"
                title="Pictures"
                files={storageData.picturesFiles}
                sizeGb={storageData.pictures}
                color="#FF8AD6"
              />
              <LegendItem
                iconName="file-text"
                title="Docs"
                files={storageData.documentsFiles}
                sizeGb={storageData.documents}
                color="#FFE06E"
              />
            </View>
          </View>
        </FadeInView>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  navBar: {
    paddingHorizontal: 16,
    paddingTop: 52,
    paddingBottom: 16,
  },
  backButton: {
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
  },
  promoBanner: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  bannerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 18,
    position: 'relative',
    overflow: 'hidden',
  },
  bannerText: {
    flex: 1,
  },
  playButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  botOrb: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FF77B7',
    opacity: 0.3,
  },
  orbGradient: {
    width: '100%',
    height: '100%',
    borderRadius: 20,
    backgroundColor: '#FF77B7',
  },
  titleSection: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  titleHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  usageBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.06)',
    borderRadius: 12,
    gap: 4,
  },
  usageText: {
    fontSize: 10,
  },
  gaugeCard: {
    paddingHorizontal: 16,
  },
  cardContainer: {
    borderRadius: 24,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.10,
    shadowRadius: 16,
    elevation: 8,
  },
  gaugeContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    marginBottom: 24,
  },
  gaugeSvg: {
    transform: [{ rotate: '0deg' }],
  },
  centerLabel: {
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
  },
  centerValue: {
    fontSize: 34,
    fontWeight: '700',
    lineHeight: 40,
  },
  centerUnit: {
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 20,
  },
  legend: {
    gap: 16,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  legendIcon: {
    width: 36,
    height: 36,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  legendContent: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  legendHeader: {
    flex: 1,
  },
});
