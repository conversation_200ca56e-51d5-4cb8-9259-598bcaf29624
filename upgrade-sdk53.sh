#!/bin/bash

echo "🚀 Upgrading to Expo SDK 53..."

# Clear cache and node_modules
echo "📦 Clearing cache and node_modules..."
npm cache clean --force
rm -rf node_modules package-lock.json

# Install dependencies
echo "📥 Installing dependencies..."
npm install --legacy-peer-deps

# Check for any remaining issues
echo "🔍 Checking for compatibility issues..."
npx expo doctor

echo "✅ Upgrade complete! You can now run 'npm start'"