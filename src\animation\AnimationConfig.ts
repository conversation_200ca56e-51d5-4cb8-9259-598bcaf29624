/**
 * Animation Configuration - shadcn/ui inspired
 * Centralized animation settings for consistent, subtle animations
 */

import { Easing } from 'react-native-reanimated';

// Animation Durations (in milliseconds)
export const ANIMATION_DURATION = {
  // Fast animations for micro-interactions
  fast: 150,
  // Normal animations for most UI transitions
  normal: 200,
  // Slow animations for complex transitions
  slow: 300,
  // Extra slow for page transitions
  page: 400,
} as const;

// Spring Animation Configurations
export const SPRING_CONFIG = {
  // Gentle spring for subtle interactions
  gentle: {
    damping: 20,
    stiffness: 120,
    mass: 1,
  },
  // Snappy spring for buttons and quick interactions
  snappy: {
    damping: 15,
    stiffness: 300,
    mass: 0.8,
  },
  // Bouncy spring for playful interactions
  bouncy: {
    damping: 8,
    stiffness: 200,
    mass: 1.2,
  },
  // Smooth spring for page transitions
  smooth: {
    damping: 25,
    stiffness: 100,
    mass: 1,
  },
} as const;

// Timing Animation Configurations
export const TIMING_CONFIG = {
  // Ease out for most UI animations
  easeOut: {
    duration: ANIMATION_DURATION.normal,
    easing: Easing.out(Easing.cubic),
  },
  // Ease in out for balanced animations
  easeInOut: {
    duration: ANIMATION_DURATION.normal,
    easing: Easing.inOut(Easing.cubic),
  },
  // Linear for progress indicators
  linear: {
    duration: ANIMATION_DURATION.normal,
    easing: Easing.linear,
  },
  // Fast ease out for micro-interactions
  fastEaseOut: {
    duration: ANIMATION_DURATION.fast,
    easing: Easing.out(Easing.cubic),
  },
} as const;

// Scale Values for Press Animations
export const SCALE_VALUES = {
  // Subtle scale for cards and large elements
  subtle: 0.98,
  // Normal scale for buttons
  normal: 0.95,
  // Strong scale for small elements
  strong: 0.92,
} as const;

// Opacity Values
export const OPACITY_VALUES = {
  // Disabled state
  disabled: 0.5,
  // Muted state
  muted: 0.7,
  // Pressed state
  pressed: 0.8,
  // Hidden state
  hidden: 0,
  // Visible state
  visible: 1,
} as const;

// Transform Values
export const TRANSFORM_VALUES = {
  // Slide distances
  slide: {
    small: 10,
    medium: 20,
    large: 40,
  },
  // Rotation angles
  rotate: {
    slight: 5,
    quarter: 90,
    half: 180,
    full: 360,
  },
} as const;

// Animation Presets for Common Patterns
export const ANIMATION_PRESETS = {
  // Button press animation
  buttonPress: {
    scale: SCALE_VALUES.normal,
    spring: SPRING_CONFIG.snappy,
  },
  // Card press animation
  cardPress: {
    scale: SCALE_VALUES.subtle,
    spring: SPRING_CONFIG.gentle,
  },
  // Fade in animation
  fadeIn: {
    opacity: {
      from: OPACITY_VALUES.hidden,
      to: OPACITY_VALUES.visible,
    },
    timing: TIMING_CONFIG.easeOut,
  },
  // Slide up animation
  slideUp: {
    translateY: {
      from: TRANSFORM_VALUES.slide.large,
      to: 0,
    },
    timing: TIMING_CONFIG.easeOut,
  },
  // Modal entrance
  modalEntrance: {
    scale: {
      from: 0.95,
      to: 1,
    },
    opacity: {
      from: OPACITY_VALUES.hidden,
      to: OPACITY_VALUES.visible,
    },
    timing: TIMING_CONFIG.easeOut,
  },
} as const;

// Animation Delays for Staggered Animations
export const ANIMATION_DELAYS = {
  none: 0,
  short: 50,
  medium: 100,
  long: 150,
  stagger: (index: number) => index * 50,
} as const;

// Performance Settings
export const PERFORMANCE_CONFIG = {
  // Use native driver when possible
  useNativeDriver: true,
  // Reduce motion for accessibility
  reduceMotion: false,
  // Maximum concurrent animations
  maxConcurrentAnimations: 5,
} as const;

export default {
  ANIMATION_DURATION,
  SPRING_CONFIG,
  TIMING_CONFIG,
  SCALE_VALUES,
  OPACITY_VALUES,
  TRANSFORM_VALUES,
  ANIMATION_PRESETS,
  ANIMATION_DELAYS,
  PERFORMANCE_CONFIG,
};
