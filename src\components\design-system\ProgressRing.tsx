import React, { useEffect, useRef } from 'react';
import { View, Animated, ViewStyle, StyleSheet } from 'react-native';
import { useTheme } from '../../theme';

// Simple fallback implementation without SVG for now
// TODO: Add react-native-svg implementation when ready

interface ProgressRingProps {
  value: number;
  total: number;
  size?: number;
  thickness?: number;
  color?: string;
  trailColor?: string;
  startAngle?: number;
  endAngle?: number;
  animated?: boolean;
  duration?: number;
  delay?: number;
  glow?: boolean;
  style?: ViewStyle;
  children?: React.ReactNode;
}

export const ProgressRing: React.FC<ProgressRingProps> = ({
  value,
  total,
  size = 200,
  thickness = 12,
  color = '#FF77B7',
  trailColor,
  startAngle = -210,
  endAngle = 30,
  animated = true,
  duration = 1200,
  delay = 150,
  glow = false,
  style,
  children,
}) => {
  const { theme } = useTheme();
  const animatedValue = useRef(new Animated.Value(0)).current;

  // Calculate progress percentage
  const percentage = Math.min(value / total, 1);

  useEffect(() => {
    if (animated) {
      Animated.timing(animatedValue, {
        toValue: percentage,
        duration,
        delay,
        useNativeDriver: false,
      }).start();
    } else {
      animatedValue.setValue(percentage);
    }
  }, [percentage, animated, duration, delay]);

  const defaultTrailColor = theme.mode === 'dark'
    ? 'rgba(255, 255, 255, 0.1)'
    : 'rgba(0, 0, 0, 0.1)';

  const finalTrailColor = trailColor || defaultTrailColor;

  const glowStyle = glow ? {
    shadowColor: color,
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  } : {};

  // Simple circular progress fallback
  return (
    <View style={[{ width: size, height: size }, glowStyle, style]}>
      {/* Background circle */}
      <View style={[
        styles.circle,
        {
          width: size,
          height: size,
          borderRadius: size / 2,
          borderWidth: thickness,
          borderColor: finalTrailColor,
        }
      ]} />

      {/* Progress circle */}
      <Animated.View style={[
        StyleSheet.absoluteFillObject,
        styles.circle,
        {
          width: size,
          height: size,
          borderRadius: size / 2,
          borderWidth: thickness,
          borderColor: color,
          borderTopColor: 'transparent',
          borderRightColor: 'transparent',
          transform: [
            {
              rotate: animatedValue.interpolate({
                inputRange: [0, 1],
                outputRange: ['0deg', '360deg'],
              }),
            },
          ],
        }
      ]} />

      {/* Center content */}
      {children && (
        <View style={[StyleSheet.absoluteFillObject, styles.centerContent]}>
          {children}
        </View>
      )}
    </View>
  );
};

// Multi-ring progress component for the statistics screen
interface MultiRingProgressProps {
  rings: Array<{
    value: number;
    total: number;
    color: string;
    thickness?: number;
    label?: string;
  }>;
  size?: number;
  animated?: boolean;
  style?: ViewStyle;
  children?: React.ReactNode;
}

export const MultiRingProgress: React.FC<MultiRingProgressProps> = ({
  rings,
  size = 200,
  animated = true,
  style,
  children,
}) => {
  const ringSpacing = 4;
  const baseThickness = 16;

  return (
    <View style={[{ width: size, height: size }, style]}>
      {rings.map((ring, index) => {
        const ringSize = size - (index * (baseThickness + ringSpacing) * 2);
        const thickness = ring.thickness || baseThickness;
        
        return (
          <ProgressRing
            key={index}
            value={ring.value}
            total={ring.total}
            size={ringSize}
            thickness={thickness}
            color={ring.color}
            animated={animated}
            delay={150 + (index * 100)}
            glow={index === 0} // Only outermost ring glows
            style={[
              StyleSheet.absoluteFillObject,
              {
                top: (size - ringSize) / 2,
                left: (size - ringSize) / 2,
              }
            ]}
          />
        );
      })}
      
      {/* Center content */}
      {children && (
        <View style={[StyleSheet.absoluteFillObject, styles.centerContent]}>
          {children}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  circle: {
    backgroundColor: 'transparent',
  },
  centerContent: {
    alignItems: 'center',
    justifyContent: 'center',
  },
});
