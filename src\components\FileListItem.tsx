import React from 'react';
import { View, StyleSheet, Image } from 'react-native';
import { FileListItemProps } from '../types';
import { useTheme } from '../theme';
import { FileTypeService } from '../services/FileTypeService';
import { Text } from './Text';
import { Icon, IconNames } from './Icon';
import { ScaleOnPress } from './ScaleOnPress';
import { FadeInView } from './FadeInView';
import { MediaAccessFallback } from './MediaAccessFallback';
import { AnimatedIcon } from './AnimatedIcon';
import { GlassCard } from './GlassCard';

export const FileListItem: React.FC<FileListItemProps> = ({
  item,
  viewMode,
  onPress,
  onLongPress,
  isSelected,
}) => {
  const { theme } = useTheme();
  const fileTypeService = FileTypeService.getInstance();

  // Show fallback for placeholder items (media access not available)
  if (item.isPlaceholder && item.placeholderType) {
    return (
      <MediaAccessFallback
        mediaType={item.placeholderType}
        onRetry={() => onPress?.(item)}
      />
    );
  }

  const getFileIcon = () => {
    if (item.type === 'directory') {
      return IconNames.FOLDER;
    }
    return fileTypeService.getFileIcon(item.name);
  };

  const getFileColor = () => {
    if (item.type === 'directory') {
      return theme.colors.primary;
    }
    return fileTypeService.getFileColor(item.name);
  };

  const renderListView = () => (
    <GlassCard
      style={[
        styles.listContainer,
        {
          backgroundColor: isSelected ? theme.colors.primary + '20' : 'rgba(255, 255, 255, 0.1)',
          borderColor: isSelected ? theme.colors.primary : 'transparent',
        }
      ]}
      intensity={isSelected ? 25 : 15}
      padding={0}
    >
      <View style={styles.listContent}>
        <AnimatedIcon
          name={getFileIcon()}
          size={24}
          color={getFileColor()}
          style={styles.icon}
          animation={item.type === 'directory' ? 'float' : 'none'}
          duration={3000}
        />
        <View style={styles.fileInfo}>
          <Text
            variant="body"
            color="text"
            weight="medium"
            numberOfLines={1}
            style={styles.fileName}>
            {item.name}
          </Text>
          <View style={styles.metadata}>
            <Text variant="caption" color="textSecondary">
              {fileTypeService.formatDate(item.modifiedDate)}
            </Text>
            {item.type === 'file' && (
              <>
                <Text variant="caption" color="textSecondary"> • </Text>
                <Text variant="caption" color="textSecondary">
                  {fileTypeService.formatFileSize(item.size)}
                </Text>
              </>
            )}
          </View>
        </View>
      </View>
      {isSelected && (
        <AnimatedIcon
          name="check"
          size={20}
          color={theme.colors.primary}
          animation="bounce"
          duration={500}
        />
      )}
    </GlassCard>
  );

  const renderGridView = () => (
    <GlassCard
      style={[
        styles.gridContainer,
        {
          borderColor: isSelected ? theme.colors.primary : 'transparent',
          borderWidth: isSelected ? 2 : 0,
        }
      ]}
      intensity={isSelected ? 25 : 15}
      padding={12}
    >
      <View style={styles.gridIconContainer}>
        {item.thumbnail && item.mimeType?.startsWith('image/') ? (
          <View style={styles.thumbnailContainer}>
            <Image
              source={{ uri: item.thumbnail }}
              style={styles.thumbnail}
              resizeMode="cover"
            />
            {isSelected && (
              <View style={[
                styles.selectionOverlay,
                { backgroundColor: theme.colors.primary + '80' }
              ]}>
                <AnimatedIcon name="check" size={16} color="#FFFFFF" animation="bounce" />
              </View>
            )}
          </View>
        ) : (
          <>
            <View style={[
              styles.iconBackground,
              { backgroundColor: getFileColor() + '20' }
            ]}>
              <AnimatedIcon
                name={getFileIcon()}
                size={32}
                color={getFileColor()}
                animation={item.type === 'directory' ? 'pulse' : 'none'}
                duration={2000}
              />
            </View>
            {isSelected && (
              <View style={[
                styles.selectionBadge,
                { backgroundColor: theme.colors.primary }
              ]}>
                <AnimatedIcon
                  name="check"
                  size={12}
                  color="#FFFFFF"
                  animation="bounce"
                />
              </View>
            )}
          </>
        )}
      </View>
      <Text
        variant="caption"
        color="text"
        weight="medium"
        numberOfLines={2}
        style={styles.gridFileName}>
        {item.name}
      </Text>
      {item.type === 'file' && (
        <Text variant="caption" color="textSecondary" numberOfLines={1}>
          {fileTypeService.formatFileSize(item.size)}
        </Text>
      )}
    </GlassCard>
  );

  const renderDetailedView = () => (
    <View style={[
      styles.detailedContainer,
      {
        backgroundColor: isSelected ? theme.colors.primary + '20' : 'transparent',
        borderColor: isSelected ? theme.colors.primary : 'transparent',
      }
    ]}>
      <View style={styles.detailedContent}>
        <Icon
          name={getFileIcon()}
          size={28}
          color={getFileColor()}
          style={styles.icon}
        />
        <View style={styles.detailedInfo}>
          <Text
            variant="body"
            color="text"
            weight="medium"
            numberOfLines={1}
            style={styles.fileName}>
            {item.name}
          </Text>
          <View style={styles.detailedMetadata}>
            <View style={styles.metadataRow}>
              <Text variant="caption" color="textSecondary">
                Modified: {fileTypeService.formatDate(item.modifiedDate)}
              </Text>
            </View>
            <View style={styles.metadataRow}>
              <Text variant="caption" color="textSecondary">
                Type: {item.type === 'directory' ? 'Folder' : fileTypeService.getFileCategory(item.name)}
              </Text>
              {item.type === 'file' && (
                <>
                  <Text variant="caption" color="textSecondary"> • </Text>
                  <Text variant="caption" color="textSecondary">
                    Size: {fileTypeService.formatFileSize(item.size)}
                  </Text>
                </>
              )}
            </View>
          </View>
        </View>
      </View>
      {isSelected && (
        <Icon
          name="check"
          size={20}
          color={theme.colors.primary}
        />
      )}
    </View>
  );

  const renderContent = () => {
    switch (viewMode) {
      case 'grid':
        return renderGridView();
      case 'detailed':
        return renderDetailedView();
      default:
        return renderListView();
    }
  };

  return (
    <FadeInView
      direction="up"
      distance={10}
      duration={200}
      style={viewMode === 'grid' ? styles.gridWrapper : {}}>
      <ScaleOnPress
        onPress={onPress}
        onLongPress={onLongPress}
        scaleValue={0.98}>
        {renderContent()}
      </ScaleOnPress>
    </FadeInView>
  );
};

const styles = StyleSheet.create({
  // List view styles
  listContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderRadius: 12,
    marginHorizontal: 16,
    marginVertical: 4,
  },
  listContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  
  // Grid view styles
  gridWrapper: {
    flex: 1,
    margin: 4,
  },
  gridContainer: {
    borderRadius: 16,
    alignItems: 'center',
    minHeight: 120,
    justifyContent: 'space-between',
  },
  gridIconContainer: {
    position: 'relative',
    marginBottom: 8,
  },
  thumbnailContainer: {
    width: 60,
    height: 60,
    borderRadius: 12,
    overflow: 'hidden',
    position: 'relative',
  },
  thumbnail: {
    width: '100%',
    height: '100%',
  },
  selectionOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 12,
  },
  iconBackground: {
    width: 60,
    height: 60,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectionBadge: {
    position: 'absolute',
    top: -4,
    right: -4,
    width: 20,
    height: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  gridFileName: {
    textAlign: 'center',
    marginBottom: 4,
  },
  
  // Detailed view styles
  detailedContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderWidth: 1,
    borderRadius: 8,
    marginHorizontal: 16,
    marginVertical: 2,
  },
  detailedContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  detailedInfo: {
    flex: 1,
  },
  detailedMetadata: {
    marginTop: 4,
  },
  metadataRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 2,
  },
  
  // Common styles
  icon: {
    marginRight: 12,
  },
  fileInfo: {
    flex: 1,
  },
  fileName: {
    marginBottom: 2,
  },
  metadata: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});