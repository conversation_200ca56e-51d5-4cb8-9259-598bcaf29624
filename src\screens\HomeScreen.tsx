import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Dimensions,
  Animated,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useTheme } from '../theme';
import { Text } from '../components/Text';
import { Icon } from '../components/Icon';
import { FadeInView } from '../components/FadeInView';
import { ScaleOnPress } from '../components/ScaleOnPress';
import { DeviceFileSystemService } from '../services/DeviceFileSystemService';
import { SearchService } from '../services/SearchService';
import { FileTypeService } from '../services/FileTypeService';
import { FileItem } from '../types';

const { width: screenWidth } = Dimensions.get('window');

interface HomeScreenProps {
  onNavigateToStatistics?: () => void;
  onNavigateToFolder?: () => void;
  onNavigateToDeviceFiles?: () => void;
  onNavigateToChat?: () => void;
}

export const HomeScreen: React.FC<HomeScreenProps> = ({
  onNavigateToStatistics,
  onNavigateToFolder,
  onNavigateToDeviceFiles,
  onNavigateToChat,
}) => {
  const { theme } = useTheme();
  const [storageInfo, setStorageInfo] = useState({
    used: 0,
    total: 0,
    usedPercentage: 0,
  });
  const [recentFiles, setRecentFiles] = useState<FileItem[]>([]);

  useEffect(() => {
    loadStorageInfo();
    loadRecentFiles();
  }, []);

  const loadStorageInfo = async () => {
    try {
      const deviceService = DeviceFileSystemService.getInstance();
      const info = await deviceService.getDeviceStorageInfo();
      if (info) {
        setStorageInfo({
          used: Math.round(info.used / (1024 * 1024 * 1024)),
          total: Math.round(info.total / (1024 * 1024 * 1024)),
          usedPercentage: (info.used / info.total) * 100,
        });
      }
    } catch (error) {
      console.error('Failed to load storage info:', error);
      // Set fallback values
      setStorageInfo({
        used: 32,
        total: 64,
        usedPercentage: 50,
      });
    }
  };

  const loadRecentFiles = async () => {
    try {
      const searchService = SearchService.getInstance();
      const files = await searchService.getRecentFiles(2);
      setRecentFiles(files);
    } catch (error) {
      console.error('Failed to load recent files:', error);
    }
  };

  // Action Button Component matching reference
  const ActionButton: React.FC<{
    iconName: string;
    backgroundColor: string;
    onPress: () => void;
  }> = ({ iconName, backgroundColor, onPress }) => (
    <ScaleOnPress onPress={onPress}>
      <View style={[styles.actionButton, { backgroundColor }]}>
        <Icon name={iconName} size={24} color="#FFFFFF" />
      </View>
    </ScaleOnPress>
  );

  const StatisticsCard = () => (
    <View style={[styles.statisticsCard, { backgroundColor: theme.colors.surface }]}>
      <View style={styles.cardHeader}>
        <Text style={styles.cardTitle}>Statistics</Text>
        <TouchableOpacity>
          <Icon name="more-horizontal" size={16} color={theme.colors.textSecondary} />
        </TouchableOpacity>
      </View>

      <View style={styles.pieChartContainer}>
        <View style={styles.pieChart}>
          <View style={[styles.pieSegment, { backgroundColor: '#FFE06E', flex: 2 }]} />
          <View style={[styles.pieSegment, { backgroundColor: '#FF8AD6', flex: 1.5 }]} />
          <View style={[styles.pieSegment, { backgroundColor: '#7BC8FF', flex: 1 }]} />
        </View>
      </View>
    </View>
  );

  const SharedWithCard = () => (
    <View style={[styles.sharedCard, { backgroundColor: '#F8C2D9' }]}>
      <View style={styles.cardHeader}>
        <Text style={styles.cardTitle}>Shared with</Text>
        <TouchableOpacity>
          <Icon name="more-horizontal" size={16} color={theme.colors.textSecondary} />
        </TouchableOpacity>
      </View>
      <View style={styles.avatarContainer}>
        <View style={[styles.sharedAvatar, { backgroundColor: '#FF8AD6' }]}>
          <Text style={styles.avatarText}>A</Text>
        </View>
        <View style={[styles.sharedAvatar, { backgroundColor: '#7BC8FF', marginLeft: -8 }]}>
          <Text style={styles.avatarText}>B</Text>
        </View>
        <View style={[styles.sharedAvatar, { backgroundColor: '#9BE7C4', marginLeft: -8 }]}>
          <Text style={styles.avatarText}>C</Text>
        </View>
      </View>
    </View>
  );

  const RecentFileItem = ({
    iconName,
    fileName,
    date,
    iconColor,
    onPress
  }: {
    iconName: string;
    fileName: string;
    date: string;
    iconColor: string;
    onPress: () => void;
  }) => (
    <TouchableOpacity style={styles.recentFileItem} onPress={onPress} activeOpacity={0.8}>
      <View style={[styles.fileIcon, { backgroundColor: iconColor }]}>
        <Icon name={iconName} size={20} color="#FFFFFF" />
      </View>
      <View style={styles.fileInfo}>
        <Text style={styles.fileName}>{fileName}</Text>
        <Text style={styles.fileDate}>{date}</Text>
      </View>
      <TouchableOpacity>
        <Icon name="more-horizontal" size={20} color={theme.colors.textSecondary} />
      </TouchableOpacity>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* Gradient Background */}
      <LinearGradient
        colors={['#FEFEFE', '#F9F7FF', '#F5F3FF']}
        style={styles.gradientBackground}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <FadeInView style={styles.header} delay={0}>
          <View style={styles.headerContent}>
            <View>
              <Text style={styles.welcomeText}>Welcome Back</Text>
              <Text style={styles.nameText}>Anna 👋</Text>
            </View>
            <View style={styles.avatar}>
              <Image
                source={{ uri: 'https://via.placeholder.com/50x50/FF8AD6/FFFFFF?text=A' }}
                style={styles.avatarImage}
              />
            </View>
          </View>
        </FadeInView>

        {/* Storage Section */}
        <FadeInView style={styles.storageSection} delay={100}>
          <Text style={styles.storageText}>
            {storageInfo.used}/<Text style={styles.storageTotal}>{storageInfo.total}</Text> GB
          </Text>

          <ScaleOnPress onPress={onNavigateToDeviceFiles || (() => {})}>
            <View style={styles.addFilesButton}>
              <Icon name="plus" size={16} color={theme.colors.primary} />
              <Text style={styles.addFilesText}>Add files</Text>
            </View>
          </ScaleOnPress>
        </FadeInView>

        {/* Action Buttons */}
        <FadeInView style={styles.actionButtons} delay={200}>
          <ActionButton
            iconName="plus"
            backgroundColor="#7BC8FF"
            onPress={onNavigateToDeviceFiles || (() => {})}
          />
          <ActionButton
            iconName="download"
            backgroundColor="#A855F7"
            onPress={() => console.log('Download')}
          />
          <ActionButton
            iconName="share"
            backgroundColor="#FF8AD6"
            onPress={() => console.log('Share')}
          />
          <ActionButton
            iconName="folder"
            backgroundColor="#9BE7C4"
            onPress={() => console.log('Folder')}
          />
        </FadeInView>

        {/* Cards Row */}
        <FadeInView style={styles.cardsRow} delay={300}>
          <StatisticsCard />
          <SharedWithCard />
        </FadeInView>

        {/* Recent Files */}
        <FadeInView style={styles.recentSection} delay={400}>
          <View style={styles.recentHeader}>
            <Text style={styles.recentTitle}>Recent files</Text>
            <TouchableOpacity>
              <Icon name="search" size={20} color={theme.colors.textSecondary} />
            </TouchableOpacity>
          </View>

          <View style={styles.recentList}>
            {recentFiles.length > 0 ? (
              recentFiles.map((file, index) => {
                const fileTypeService = FileTypeService.getInstance();
                const iconName = file.mimeType?.startsWith('image/') ? 'image' : 'file-text';
                const iconColor = index === 0 ? '#F8C2D9' : '#FFE06E';

                return (
                  <RecentFileItem
                    key={file.path}
                    iconName={iconName}
                    fileName={file.name}
                    date={fileTypeService.formatDate(file.modifiedDate)}
                    iconColor={iconColor}
                    onPress={onNavigateToFolder || (() => {})}
                  />
                );
              })
            ) : (
              <RecentFileItem
                iconName="folder"
                fileName="No recent files"
                date="Start browsing files"
                iconColor="#6B7280"
                onPress={onNavigateToDeviceFiles || (() => {})}
              />
            )}
          </View>
        </FadeInView>
      </ScrollView>

      {/* Bottom Navigation */}
      <FadeInView style={styles.bottomNav} delay={500}>
        <ScaleOnPress>
          <View style={styles.navItem}>
            <Icon name="home" size={20} color="#FFFFFF" />
          </View>
        </ScaleOnPress>
        <ScaleOnPress onPress={onNavigateToDeviceFiles || (() => {})}>
          <View style={styles.navItem}>
            <Icon name="search" size={20} color="rgba(255, 255, 255, 0.6)" />
          </View>
        </ScaleOnPress>
        <ScaleOnPress>
          <View style={styles.navItem}>
            <Icon name="settings" size={20} color="rgba(255, 255, 255, 0.6)" />
          </View>
        </ScaleOnPress>
      </FadeInView>

      {/* Chat FAB */}
      <FadeInView style={styles.chatFab} delay={600}>
        <ScaleOnPress onPress={onNavigateToChat || (() => {})}>
          <Icon name="message-circle" size={24} color="#FFFFFF" />
        </ScaleOnPress>
      </FadeInView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradientBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  scrollView: {
    flex: 1,
    paddingTop: 50,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 20,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  welcomeText: {
    fontSize: 16,
    color: '#1A1A1A',
    fontWeight: '400',
    marginBottom: 4,
  },
  nameText: {
    fontSize: 28,
    fontWeight: '700',
    color: '#1A1A1A',
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    overflow: 'hidden',
  },
  avatarImage: {
    width: '100%',
    height: '100%',
    borderRadius: 25,
  },
  avatarText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  storageSection: {
    paddingHorizontal: 20,
    paddingBottom: 30,
  },
  storageText: {
    fontSize: 48,
    fontWeight: '800',
    color: '#1A1A1A',
    marginBottom: 20,
    letterSpacing: -1,
  },
  storageTotal: {
    color: '#AAAAAA',
    fontWeight: '400',
  },
  addFilesButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 20,
    alignSelf: 'flex-start',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  addFilesText: {
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '500',
    color: '#1A1A1A',
  },
  actionButtons: {
    flexDirection: 'row',
    paddingHorizontal: 30,
    paddingBottom: 40,
    justifyContent: 'space-around',
  },
  actionButton: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 8,
  },
  cardsRow: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingBottom: 40,
    gap: 20,
  },
  statisticsCard: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    padding: 24,
    borderRadius: 28,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.12,
    shadowRadius: 16,
    elevation: 6,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A1A1A',
  },
  pieChartContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    height: 80,
  },
  pieChart: {
    width: 60,
    height: 60,
    borderRadius: 30,
    flexDirection: 'row',
    overflow: 'hidden',
  },
  pieSegment: {
    height: '100%',
  },
  sharedCard: {
    flex: 1,
    padding: 24,
    borderRadius: 28,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.12,
    shadowRadius: 16,
    elevation: 6,
  },
  avatarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 16,
  },
  sharedAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  recentSection: {
    paddingHorizontal: 20,
    paddingBottom: 100,
  },
  recentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  recentTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A1A1A',
  },
  recentList: {
    gap: 16,
  },
  recentFileItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    padding: 20,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 4,
  },
  fileIcon: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  fileInfo: {
    flex: 1,
  },
  fileName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1A1A1A',
    marginBottom: 4,
  },
  fileDate: {
    fontSize: 14,
    color: '#666666',
  },
  bottomNav: {
    position: 'absolute',
    bottom: 40,
    left: '50%',
    transform: [{ translateX: -80 }],
    width: 160,
    flexDirection: 'row',
    backgroundColor: 'rgba(0, 0, 0, 0.95)',
    borderRadius: 32,
    paddingVertical: 18,
    paddingHorizontal: 24,
    justifyContent: 'space-around',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.4,
    shadowRadius: 20,
    elevation: 12,
  },
  navItem: {
    padding: 8,
  },
  chatFab: {
    position: 'absolute',
    bottom: 130,
    right: 30,
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#FF8AD6',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#FF8AD6',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.5,
    shadowRadius: 20,
    elevation: 12,
  },
});
