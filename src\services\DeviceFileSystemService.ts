import * as FileSystem from 'expo-file-system';
import * as MediaLibrary from 'expo-media-library';
import * as ImagePicker from 'expo-image-picker';
import { FileItem, FilePermissions, FileManagerError } from '../types';

export interface DeviceDirectory {
  id: string;
  name: string;
  path: string;
  type: 'app' | 'media' | 'external' | 'system';
  icon: string;
  description: string;
  accessible: boolean;
}

export class DeviceFileSystemService {
  private static instance: DeviceFileSystemService;
  private mediaLibraryPermission: boolean = false;
  private cameraPermission: boolean = false;

  public static getInstance(): DeviceFileSystemService {
    if (!DeviceFileSystemService.instance) {
      DeviceFileSystemService.instance = new DeviceFileSystemService();
    }
    return DeviceFileSystemService.instance;
  }

  private constructor() {
    this.initializePermissions();
  }

  /**
   * Initialize and request necessary permissions
   */
  async initializePermissions(): Promise<void> {
    try {
      // On Android 13+ we should request separate permissions for media types
      const mediaPermission = await MediaLibrary.requestPermissionsAsync();
      this.mediaLibraryPermission = mediaPermission.granted === true || mediaPermission.status === 'granted';

      // Camera permission for taking photos
      const cameraStatus = await ImagePicker.requestCameraPermissionsAsync();
      this.cameraPermission = cameraStatus.granted === true || cameraStatus.status === 'granted';
    } catch (error) {
      console.error('Failed to initialize permissions:', error);
    }
  }

  /** Ensure we have media permissions, optionally show system dialog again */
  async ensureMediaPermissions(showRationale = true): Promise<boolean> {
    try {
      const permission = await MediaLibrary.getPermissionsAsync();
      if (permission.granted) {
        this.mediaLibraryPermission = true;
        return true;
      }
      if (showRationale) {
        const request = await MediaLibrary.requestPermissionsAsync();
        this.mediaLibraryPermission = request.granted === true || request.status === 'granted';
        return this.mediaLibraryPermission;
      }
      return false;
    } catch (e) {
      console.warn('ensureMediaPermissions failed:', e);
      return false;
    }
  }

  /**
   * Get available device directories
   */
  async getDeviceDirectories(): Promise<DeviceDirectory[]> {
    const directories: DeviceDirectory[] = [
      {
        id: 'app_documents',
        name: 'App Documents',
        path: FileSystem.documentDirectory || '',
        type: 'app',
        icon: 'folder',
        description: 'App-specific documents and files',
        accessible: true,
      },
      {
        id: 'app_cache',
        name: 'App Cache',
        path: FileSystem.cacheDirectory || '',
        type: 'app',
        icon: 'folder',
        description: 'Temporary app files and cache',
        accessible: true,
      },
    ];

    // Add media directories if permission granted
    if (this.mediaLibraryPermission) {
      directories.push(
        {
          id: 'photos',
          name: 'Photos',
          path: 'media://photos',
          type: 'media',
          icon: 'image',
          description: 'Device photo gallery',
          accessible: true,
        },
        {
          id: 'videos',
          name: 'Videos',
          path: 'media://videos',
          type: 'media',
          icon: 'video',
          description: 'Device video library',
          accessible: true,
        },
        {
          id: 'audio',
          name: 'Audio',
          path: 'media://audio',
          type: 'media',
          icon: 'music',
          description: 'Device audio files',
          accessible: true,
        }
      );
    }

    return directories;
  }

  /**
   * List directory contents with enhanced mobile support
   */
  async listDirectory(path: string): Promise<FileItem[]> {
    try {
      // Handle media library paths
      if (path.startsWith('media://')) {
        return await this.listMediaDirectory(path);
      }

      // Handle regular file system paths
      const items = await FileSystem.readDirectoryAsync(path);
      
      const fileItems: FileItem[] = await Promise.all(
        items.map(async (itemName) => {
          const itemPath = `${path}/${itemName}`;
          const info = await FileSystem.getInfoAsync(itemPath);
          const permissions = await this.getFilePermissions(itemPath);
          
          return {
            name: itemName,
            path: itemPath,
            type: info.isDirectory ? 'directory' : 'file',
            size: (info as any).size || 0,
            modifiedDate: (info as any).modificationTime ? new Date((info as any).modificationTime * 1000) : new Date(),
            mimeType: !info.isDirectory ? this.getMimeType(itemName) : undefined,
            isHidden: itemName.startsWith('.'),
            permissions,
            thumbnail: (await this.generateThumbnail(itemPath, info.isDirectory)) || undefined,
          };
        })
      );

      return this.sortFiles(fileItems);
    } catch (error) {
      throw this.handleError(error, 'Failed to list directory');
    }
  }

  /**
   * List media library contents
   */
  private async listMediaDirectory(path: string): Promise<FileItem[]> {
    if (!this.mediaLibraryPermission) {
      console.warn('Media library permission not granted, requesting permissions...');
      await this.initializePermissions();
      if (!this.mediaLibraryPermission) {
        // Return a placeholder item indicating media access is not available
        return this.createMediaAccessPlaceholder(path);
      }
    }

    try {
      const mediaType = path.split('://')[1];
      let mediaTypeFilter: any[];

      switch (mediaType) {
        case 'photos':
          mediaTypeFilter = ['photo'];
          break;
        case 'videos':
          mediaTypeFilter = ['video'];
          break;
        case 'audio':
          mediaTypeFilter = ['audio'];
          break;
        default:
          mediaTypeFilter = ['photo', 'video', 'audio'];
      }

      const media = await MediaLibrary.getAssetsAsync({
        mediaType: mediaTypeFilter,
        first: 100, // Reduced for better performance
        sortBy: MediaLibrary.SortBy.modificationTime,
      });

      const fileItems: FileItem[] = await Promise.all(
        media.assets.map(async (asset) => {
          try {
            const assetInfo = await MediaLibrary.getAssetInfoAsync(asset);

            return {
              name: asset.filename,
              path: asset.uri,
              type: 'file' as const,
              size: (assetInfo as any).fileSize || this.estimateFileSize(asset),
              modifiedDate: new Date(asset.modificationTime),
              mimeType: this.getMimeTypeFromMediaType(asset.mediaType),
              isHidden: false,
              permissions: {
                readable: true,
                writable: false, // Media library files are typically read-only
                executable: false,
              },
              thumbnail: asset.uri, // Use the asset URI as thumbnail for now
              mediaAsset: asset, // Store the original asset for additional operations
            };
          } catch (assetError) {
            console.warn(`Failed to get info for asset ${asset.filename}:`, assetError);
            // Return basic info if detailed info fails
            return {
              name: asset.filename,
              path: asset.uri,
              type: 'file' as const,
              size: this.estimateFileSize(asset),
              modifiedDate: new Date(asset.modificationTime),
              mimeType: this.getMimeTypeFromMediaType(asset.mediaType),
              isHidden: false,
              permissions: {
                readable: true,
                writable: false,
                executable: false,
              },
              thumbnail: asset.uri,
              mediaAsset: asset,
            };
          }
        })
      );

      return fileItems;
    } catch (error) {
      console.error('Failed to list media directory:', error);
      return this.createMediaAccessPlaceholder(path);
    }
  }

  /**
   * Create placeholder items when media access is not available
   */
  private createMediaAccessPlaceholder(path: string): FileItem[] {
    const mediaType = path.split('://')[1] as 'photos' | 'videos' | 'audio';

    const getPlaceholderInfo = () => {
      switch (mediaType) {
        case 'photos':
          return { name: 'Photos not available in Expo Go', icon: '📷' };
        case 'videos':
          return { name: 'Videos not available in Expo Go', icon: '🎥' };
        case 'audio':
          return { name: 'Audio not available in Expo Go', icon: '🎵' };
        default:
          return { name: 'Media not available in Expo Go', icon: '📁' };
      }
    };

    const info = getPlaceholderInfo();

    return [{
      name: info.name,
      path: `${path}/placeholder`,
      type: 'file' as const,
      size: 0,
      modifiedDate: new Date(),
      mimeType: 'text/plain',
      isHidden: false,
      permissions: {
        readable: true,
        writable: false,
        executable: false,
      },
      isPlaceholder: true,
      placeholderType: mediaType,
    }];
  }

  /**
   * Generate thumbnail for files
   */
  private async generateThumbnail(filePath: string, isDirectory: boolean): Promise<string | undefined> {
    if (isDirectory) return undefined;

    try {
      const mimeType = this.getMimeType(filePath);
      
      // For images, return the file path as thumbnail
      if (mimeType?.startsWith('image/')) {
        return filePath;
      }

      // For videos, we could generate thumbnails in the future
      // For now, return undefined
      return undefined;
    } catch (error) {
      return undefined;
    }
  }

  /**
   * Take photo with camera
   */
  async takePhoto(): Promise<FileItem | null> {
    if (!this.cameraPermission) {
      const permission = await ImagePicker.requestCameraPermissionsAsync();
      if (permission.status !== 'granted') {
        throw new Error('Camera permission not granted');
      }
      this.cameraPermission = true;
    }

    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 1,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        return {
          name: `photo_${Date.now()}.jpg`,
          path: asset.uri,
          type: 'file',
          size: asset.fileSize || 0,
          modifiedDate: new Date(),
          mimeType: 'image/jpeg',
          isHidden: false,
          permissions: {
            readable: true,
            writable: true,
            executable: false,
          },
          thumbnail: asset.uri,
        };
      }

      return null;
    } catch (error) {
      throw this.handleError(error, 'Failed to take photo');
    }
  }

  /**
   * Pick image from gallery
   */
  async pickImageFromGallery(): Promise<FileItem | null> {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 1,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        return {
          name: asset.fileName || `image_${Date.now()}.jpg`,
          path: asset.uri,
          type: 'file',
          size: asset.fileSize || 0,
          modifiedDate: new Date(),
          mimeType: asset.mimeType || 'image/jpeg',
          isHidden: false,
          permissions: {
            readable: true,
            writable: false,
            executable: false,
          },
          thumbnail: asset.uri,
        };
      }

      return null;
    } catch (error) {
      throw this.handleError(error, 'Failed to pick image');
    }
  }

  /**
   * Get storage information for device
   */
  async getDeviceStorageInfo(): Promise<{ free: number; total: number; used: number }> {
    try {
      const freeSpace = await FileSystem.getFreeDiskStorageAsync();
      const totalSpace = await FileSystem.getTotalDiskCapacityAsync();
      const usedSpace = totalSpace - freeSpace;

      return {
        free: freeSpace,
        total: totalSpace,
        used: usedSpace,
      };
    } catch (error) {
      throw this.handleError(error, 'Failed to get storage info');
    }
  }

  // Helper methods
  private sortFiles(files: FileItem[]): FileItem[] {
    return files.sort((a, b) => {
      // Directories first
      if (a.type !== b.type) {
        return a.type === 'directory' ? -1 : 1;
      }
      // Then by name
      return a.name.toLowerCase().localeCompare(b.name.toLowerCase());
    });
  }

  private getMimeTypeFromMediaType(mediaType: any): string {
    switch (mediaType) {
      case 'photo':
        return 'image/jpeg';
      case 'video':
        return 'video/mp4';
      case 'audio':
        return 'audio/mpeg';
      default:
        return 'application/octet-stream';
    }
  }

  /**
   * Estimate file size based on media type and duration
   */
  private estimateFileSize(asset: any): number {
    try {
      // If we have width and height, estimate based on that
      if (asset.width && asset.height) {
        const pixels = asset.width * asset.height;
        switch (asset.mediaType) {
          case 'photo':
            // Estimate ~3 bytes per pixel for JPEG
            return Math.round(pixels * 3);
          case 'video':
            // Estimate based on duration and resolution
            const duration = asset.duration || 30; // Default 30 seconds
            const bitrate = pixels > 2000000 ? 8000000 : 4000000; // 8Mbps for HD, 4Mbps for SD
            return Math.round((bitrate * duration) / 8); // Convert bits to bytes
          case 'audio':
            // Estimate ~128kbps for audio
            const audioDuration = asset.duration || 180; // Default 3 minutes
            return Math.round((128000 * audioDuration) / 8); // Convert bits to bytes
          default:
            return 1024 * 1024; // Default 1MB
        }
      }

      // Fallback estimates
      switch (asset.mediaType) {
        case 'photo':
          return 2 * 1024 * 1024; // 2MB
        case 'video':
          return 50 * 1024 * 1024; // 50MB
        case 'audio':
          return 5 * 1024 * 1024; // 5MB
        default:
          return 1024 * 1024; // 1MB
      }
    } catch (error) {
      console.warn('Failed to estimate file size:', error);
      return 1024 * 1024; // Default 1MB
    }
  }

  private getMimeType(filename: string): string | undefined {
    const extension = filename.toLowerCase().split('.').pop();
    
    const mimeTypes: Record<string, string> = {
      // Images
      jpg: 'image/jpeg', jpeg: 'image/jpeg', png: 'image/png',
      gif: 'image/gif', webp: 'image/webp', svg: 'image/svg+xml',
      
      // Videos
      mp4: 'video/mp4', avi: 'video/x-msvideo', mov: 'video/quicktime',
      wmv: 'video/x-ms-wmv', mkv: 'video/x-matroska',
      
      // Audio
      mp3: 'audio/mpeg', wav: 'audio/wav', ogg: 'audio/ogg',
      m4a: 'audio/mp4', flac: 'audio/flac',
      
      // Documents
      pdf: 'application/pdf', doc: 'application/msword',
      docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      txt: 'text/plain', md: 'text/markdown',
    };

    return extension ? mimeTypes[extension] : undefined;
  }

  private async getFilePermissions(path: string): Promise<FilePermissions> {
    try {
      return {
        readable: true,
        writable: !path.startsWith('media://'), // Media files are typically read-only
        executable: false,
      };
    } catch (error) {
      return {
        readable: false,
        writable: false,
        executable: false,
      };
    }
  }

  async getStorageInfo(): Promise<{
    totalSpace: number;
    usedSpace: number;
    freeSpace: number;
  }> {
    try {
      // Mock storage info for demo purposes
      // In a real app, you'd use native modules to get actual storage info
      const totalSpace = 860 * 1024 * 1024 * 1024; // 860 GB in bytes
      const usedSpace = 432 * 1024 * 1024 * 1024; // 432 GB in bytes
      const freeSpace = totalSpace - usedSpace;

      return {
        totalSpace,
        usedSpace,
        freeSpace,
      };
    } catch (error) {
      console.error('Failed to get storage info:', error);
      // Return mock data as fallback
      return {
        totalSpace: 860 * 1024 * 1024 * 1024,
        usedSpace: 432 * 1024 * 1024 * 1024,
        freeSpace: 428 * 1024 * 1024 * 1024,
      };
    }
  }

  private handleError(error: any, message: string): Error {
    console.error(`DeviceFileSystemService Error: ${message}`, error);
    
    if (error.code === 'ENOENT') {
      return new Error(`${FileManagerError.FILE_NOT_FOUND}: ${message}`);
    } else if (error.code === 'EACCES') {
      return new Error(`${FileManagerError.PERMISSION_DENIED}: ${message}`);
    } else {
      return new Error(`${FileManagerError.INVALID_OPERATION}: ${message}`);
    }
  }
}
