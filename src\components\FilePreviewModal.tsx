import React from 'react';
import {
  Modal,
  View,
  StyleSheet,
  StatusBar,
  Platform,
} from 'react-native';
import { FileItem } from '../types';
import { useTheme } from '../theme';
import { FilePreview } from './FilePreview';

interface FilePreviewModalProps {
  visible: boolean;
  file: FileItem | null;
  onClose: () => void;
  onShare?: () => void;
  onDelete?: () => void;
}

export const FilePreviewModal: React.FC<FilePreviewModalProps> = ({
  visible,
  file,
  onClose,
  onShare,
  onDelete,
}) => {
  const { theme, isDark } = useTheme();

  if (!file) return null;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="fullScreen"
      onRequestClose={onClose}>
      <StatusBar
        barStyle={isDark ? 'light-content' : 'dark-content'}
        backgroundColor={theme.colors.background}
        translucent={Platform.OS === 'android'}
      />
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <FilePreview
          file={file}
          onClose={onClose}
          onShare={onShare || (() => {})}
          onDelete={onDelete || (() => {})}
        />
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
