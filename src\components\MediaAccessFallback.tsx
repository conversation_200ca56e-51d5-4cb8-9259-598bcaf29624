import React from 'react';
import { View, StyleSheet, TouchableOpacity, Linking } from 'react-native';
import { Text } from './Text';
import { Icon, IconNames } from './Icon';
import { useTheme } from '../theme';
import { ScaleOnPress } from './ScaleOnPress';
import { FadeInView } from './FadeInView';

interface MediaAccessFallbackProps {
  mediaType: 'photos' | 'videos' | 'audio';
  onRetry?: () => void;
}

export const MediaAccessFallback: React.FC<MediaAccessFallbackProps> = ({
  mediaType,
  onRetry,
}) => {
  const { theme } = useTheme();

  const getMediaInfo = () => {
    switch (mediaType) {
      case 'photos':
        return {
          icon: IconNames.IMAGE,
          title: 'Photos Not Available',
          description: 'Photo access is limited in Expo Go on Android 13+',
          color: '#FF6B6B',
        };
      case 'videos':
        return {
          icon: IconNames.VIDEO,
          title: 'Videos Not Available',
          description: 'Video access is limited in Expo Go on Android 13+',
          color: '#4ECDC4',
        };
      case 'audio':
        return {
          icon: IconNames.MUSIC,
          title: 'Audio Not Available',
          description: 'Audio access is limited in Expo Go on Android 13+',
          color: '#45B7D1',
        };
    }
  };

  const mediaInfo = getMediaInfo();

  const handleLearnMore = () => {
    Linking.openURL('https://docs.expo.dev/develop/development-builds/create-a-build/');
  };

  return (
    <FadeInView style={[styles.container, { backgroundColor: theme.colors.card }]}>
      <View style={[styles.iconContainer, { backgroundColor: `${mediaInfo.color}20` }]}>
        <Icon name={mediaInfo.icon} size={48} color={mediaInfo.color} />
      </View>
      
      <Text variant="heading" color="text" weight="bold" style={styles.title}>
        {mediaInfo.title}
      </Text>
      
      <Text variant="body" color="textSecondary" style={styles.description}>
        {mediaInfo.description}
      </Text>
      
      <Text variant="caption" color="textSecondary" style={styles.explanation}>
        To access your device's media files, you'll need to create a development build instead of using Expo Go.
      </Text>

      <View style={styles.buttonContainer}>
        {onRetry && (
          <ScaleOnPress
            style={[styles.button, styles.retryButton, { backgroundColor: theme.colors.primary }]}
            onPress={onRetry}
          >
            <Icon name={IconNames.REFRESH} size={16} color="white" />
            <Text variant="body" color="white" weight="medium" style={styles.buttonText}>
              Try Again
            </Text>
          </ScaleOnPress>
        )}
        
        <ScaleOnPress
          style={[styles.button, styles.learnButton, { borderColor: theme.colors.border }]}
          onPress={handleLearnMore}
        >
          <Icon name={IconNames.INFO} size={16} color={theme.colors.primary} />
          <Text variant="body" color="primary" weight="medium" style={styles.buttonText}>
            Learn More
          </Text>
        </ScaleOnPress>
      </View>
    </FadeInView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
    margin: 16,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  iconContainer: {
    width: 96,
    height: 96,
    borderRadius: 48,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 20,
    textAlign: 'center',
    marginBottom: 12,
  },
  description: {
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 22,
  },
  explanation: {
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 18,
    paddingHorizontal: 16,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 12,
    minWidth: 120,
    justifyContent: 'center',
  },
  retryButton: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  learnButton: {
    borderWidth: 1,
    backgroundColor: 'transparent',
  },
  buttonText: {
    marginLeft: 8,
  },
});
