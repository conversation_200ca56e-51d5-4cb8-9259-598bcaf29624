# Assets Directory

This directory contains image assets for the enhanced file manager app.

## Required Images

The following images are referenced in the enhanced screens but need to be added:

### User Images
- `anna.jpg` - User avatar for the home screen header
- `p1.jpg` - First person avatar for shared files
- `p2.jpg` - Second person avatar for shared files  
- `p3.jpg` - Third person avatar for shared files

### App Images
- `bot_orb.png` - AI assistant avatar for chat screen
- `toy_cluster.png` - Hero image for the compositions tutorial folder

## Placeholder Implementation

For development purposes, you can:

1. Use any profile photos for the user avatars
2. Use a simple bot icon or emoji for the bot avatar
3. Use any sample image for the toy cluster

## Production Assets

For production, ensure all images are:
- Optimized for mobile (WebP format recommended)
- Available in multiple resolutions (@1x, @2x, @3x)
- Properly licensed for commercial use
