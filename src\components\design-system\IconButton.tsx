import React from 'react';
import { TouchableOpacity, ViewStyle, StyleSheet } from 'react-native';
import { useTheme } from '../../theme';
import { Icon, IconNames } from '../Icon';
import { ScaleOnPress } from '../ScaleOnPress';

interface IconButtonProps {
  icon: IconNames;
  onPress: () => void;
  size?: 'small' | 'medium' | 'large';
  variant?: 'default' | 'primary' | 'secondary' | 'ghost';
  disabled?: boolean;
  style?: ViewStyle;
}

export const IconButton: React.FC<IconButtonProps> = ({
  icon,
  onPress,
  size = 'medium',
  variant = 'default',
  disabled = false,
  style,
}) => {
  const { theme } = useTheme();

  const getSizeConfig = () => {
    switch (size) {
      case 'small':
        return { containerSize: 36, iconSize: 18 };
      case 'large':
        return { containerSize: 56, iconSize: 28 };
      default:
        return { containerSize: 44, iconSize: 22 };
    }
  };

  const getButtonStyle = (): ViewStyle => {
    const { containerSize } = getSizeConfig();
    const baseStyle: ViewStyle = {
      width: containerSize,
      height: containerSize,
      borderRadius: 14,
      alignItems: 'center',
      justifyContent: 'center',
      ...theme.elevation.x1,
    };

    switch (variant) {
      case 'primary':
        return {
          ...baseStyle,
          backgroundColor: theme.colors.primary,
        };
      case 'secondary':
        return {
          ...baseStyle,
          backgroundColor: theme.colors.surface,
          borderWidth: 1,
          borderColor: theme.colors.border,
        };
      case 'ghost':
        return {
          ...baseStyle,
          backgroundColor: 'transparent',
          shadowOpacity: 0,
          elevation: 0,
        };
      default:
        return {
          ...baseStyle,
          backgroundColor: theme.colors.surface,
        };
    }
  };

  const getIconColor = () => {
    if (disabled) return theme.colors.textTertiary;
    
    switch (variant) {
      case 'primary':
        return theme.mode === 'dark' ? theme.colors.text : '#FFFFFF';
      case 'ghost':
        return theme.colors.text;
      default:
        return theme.colors.text;
    }
  };

  const { iconSize } = getSizeConfig();

  return (
    <ScaleOnPress
      style={[
        getButtonStyle(),
        disabled && styles.disabled,
        style,
      ]}
      onPress={onPress}
      disabled={disabled}
    >
      <Icon 
        name={icon} 
        size={iconSize} 
        color={getIconColor()} 
      />
    </ScaleOnPress>
  );
};

const styles = StyleSheet.create({
  disabled: {
    opacity: 0.5,
  },
});
