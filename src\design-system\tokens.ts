/**
 * Design Tokens - Foundation for the design system
 * Based on the specification requirements
 */

// Base unit for spacing calculations
export const BASE_UNIT = 4;

// Color Palette - shadcn/ui inspired
export const colors = {
  // Light theme colors
  light: {
    // Primary colors - shadcn/ui style
    primary: '#18181b',
    primaryForeground: '#fafafa',

    // Secondary colors
    secondary: '#f4f4f5',
    secondaryForeground: '#18181b',

    // Background colors
    background: '#ffffff',
    foreground: '#09090b',

    // Card colors
    card: '#ffffff',
    cardForeground: '#09090b',

    // Popover colors
    popover: '#ffffff',
    popoverForeground: '#09090b',

    // Muted colors
    muted: '#f4f4f5',
    mutedForeground: '#71717a',

    // Accent colors
    accent: '#f4f4f5',
    accentForeground: '#18181b',

    // Destructive colors
    destructive: '#ef4444',
    destructiveForeground: '#fef2f2',

    // Border and input
    border: '#e4e4e7',
    input: '#e4e4e7',
    ring: '#18181b',

    // Text colors
    text: '#09090b',
    textSecondary: '#71717a',
    textMuted: '#a1a1aa',

    // Status colors
    success: '#22c55e',
    warning: '#f59e0b',
    error: '#ef4444',
    info: '#3b82f6',

    // Data visualization colors
    dataColors: {
      pink: '#ec4899',
      yellow: '#eab308',
      blue: '#3b82f6',
      purple: '#8b5cf6',
      green: '#22c55e',
      orange: '#f97316',
    },
  },

  // Dark theme colors - shadcn/ui inspired
  dark: {
    // Primary colors - shadcn/ui dark style
    primary: '#fafafa',
    primaryForeground: '#18181b',

    // Secondary colors
    secondary: '#27272a',
    secondaryForeground: '#fafafa',

    // Background colors
    background: '#09090b',
    foreground: '#fafafa',

    // Card colors
    card: '#18181b',
    cardForeground: '#fafafa',

    // Popover colors
    popover: '#18181b',
    popoverForeground: '#fafafa',

    // Muted colors
    muted: '#18181b',
    mutedForeground: '#a1a1aa',

    // Accent colors
    accent: '#27272a',
    accentForeground: '#fafafa',

    // Destructive colors
    destructive: '#7f1d1d',
    destructiveForeground: '#fef2f2',

    // Border and input
    border: '#27272a',
    input: '#27272a',
    ring: '#d4d4d8',

    // Text colors
    text: '#fafafa',
    textSecondary: '#a1a1aa',
    textMuted: '#71717a',

    // Status colors
    success: '#22c55e',
    warning: '#f59e0b',
    error: '#ef4444',
    info: '#3b82f6',

    // Data visualization colors
    dataColors: {
      pink: '#f472b6',
      yellow: '#facc15',
      blue: '#60a5fa',
      purple: '#a78bfa',
      green: '#4ade80',
      orange: '#fb923c',
    },
  },
};

// Typography Scale
export const typography = {
  // Display styles
  display: {
    large: {
      fontSize: 48,
      lineHeight: 56,
      fontWeight: '700' as const,
      letterSpacing: -0.02,
    },
    medium: {
      fontSize: 36,
      lineHeight: 44,
      fontWeight: '700' as const,
      letterSpacing: -0.02,
    },
  },
  
  // Title styles
  title: {
    large: {
      fontSize: 28,
      lineHeight: 36,
      fontWeight: '600' as const,
      letterSpacing: -0.01,
    },
    medium: {
      fontSize: 24,
      lineHeight: 32,
      fontWeight: '600' as const,
      letterSpacing: -0.01,
    },
    small: {
      fontSize: 20,
      lineHeight: 28,
      fontWeight: '600' as const,
      letterSpacing: -0.01,
    },
  },
  
  // Body styles
  body: {
    large: {
      fontSize: 18,
      lineHeight: 28,
      fontWeight: '400' as const,
      letterSpacing: 0,
    },
    medium: {
      fontSize: 16,
      lineHeight: 24,
      fontWeight: '400' as const,
      letterSpacing: 0,
    },
    small: {
      fontSize: 14,
      lineHeight: 20,
      fontWeight: '400' as const,
      letterSpacing: 0,
    },
  },
  
  // Label and caption styles
  label: {
    fontSize: 14,
    lineHeight: 20,
    fontWeight: '500' as const,
    letterSpacing: 0.01,
  },
  
  caption: {
    fontSize: 12,
    lineHeight: 16,
    fontWeight: '400' as const,
    letterSpacing: 0.01,
  },
};

// Spacing Scale (based on 4px base unit)
export const spacing = {
  xs: BASE_UNIT,      // 4
  sm: BASE_UNIT * 2,  // 8
  md: BASE_UNIT * 3,  // 12
  lg: BASE_UNIT * 4,  // 16
  xl: BASE_UNIT * 5,  // 20
  '2xl': BASE_UNIT * 6,  // 24
  '3xl': BASE_UNIT * 8,  // 32
  '4xl': BASE_UNIT * 10, // 40
  '5xl': BASE_UNIT * 12, // 48
  '6xl': BASE_UNIT * 16, // 64
};

// Border Radius Scale - shadcn/ui inspired
export const radii = {
  none: 0,
  sm: 2,
  md: 4,
  lg: 8,
  xl: 12,
  '2xl': 16,
  '3xl': 24,
  full: 9999,
};

// Elevation/Shadow Presets
export const elevation = {
  x1: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  x2: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 4,
  },
  x3: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 8,
  },
};

// Animation Timing
export const animation = {
  timing: {
    fast: 150,
    normal: 300,
    slow: 500,
    gauge: 1200,
  },
  easing: {
    easeOut: 'ease-out',
    spring: 'spring',
  },
};

// Hit Target Sizes
export const hitTargets = {
  minimum: 44,
  comfortable: 48,
  large: 56,
};

// Progress Ring Configuration
export const progressRing = {
  thickness: 12,
  startAngle: -210,
  endAngle: 30,
  sweepAngle: 240,
  cap: 'round' as const,
};

export default {
  colors,
  typography,
  spacing,
  radii,
  elevation,
  animation,
  hitTargets,
  progressRing,
};
