/**
 * Design Tokens - Foundation for the design system
 * Based on the specification requirements
 */

// Base unit for spacing calculations
export const BASE_UNIT = 4;

// Color Palette
export const colors = {
  // Light theme colors
  light: {
    // Primary colors
    primary: '#6366F1',
    primaryAlt: '#4F46E5',
    
    // Background colors
    background: '#FFFFFF',
    backgroundAlt: '#F7F7FB',
    surface: '#FFFFFF',
    surfaceAlt: '#F9FAFB',
    card: '#FFFFFF',
    
    // Text colors
    text: '#0F172A',
    textSecondary: '#64748B',
    textTertiary: '#94A3B8',
    
    // Border colors
    border: '#E2E8F0',
    borderAlt: '#CBD5E1',
    
    // Status colors
    success: '#10B981',
    warning: '#F59E0B',
    error: '#EF4444',
    info: '#3B82F6',
    
    // Data visualization colors
    dataColors: {
      pink: '#FF8AD6',
      yellow: '#FFE06E',
      blue: '#7BC8FF',
      purple: '#A78BFA',
      green: '#34D399',
      orange: '#FB923C',
    },
    
    // Accent colors
    accent: {
      pink: '#FFE8F5',
      blue: '#EFF6FF',
      yellow: '#FFFBEB',
      purple: '#F3E8FF',
    },
  },
  
  // Dark theme colors
  dark: {
    // Primary colors
    primary: '#818CF8',
    primaryAlt: '#6366F1',
    
    // Background colors
    background: '#0F172A',
    backgroundAlt: '#1E293B',
    surface: '#12161C',
    surfaceAlt: '#161A1F',
    card: '#12161C',
    
    // Text colors
    text: '#F8FAFC',
    textSecondary: '#CBD5E1',
    textTertiary: '#64748B',
    
    // Border colors
    border: '#334155',
    borderAlt: '#475569',
    
    // Status colors
    success: '#10B981',
    warning: '#F59E0B',
    error: '#EF4444',
    info: '#3B82F6',
    
    // Data visualization colors
    dataColors: {
      pink: '#FF77B7',
      yellow: '#FFE06E',
      blue: '#7BC8FF',
      purple: '#A78BFA',
      green: '#34D399',
      orange: '#FB923C',
    },
    
    // Accent colors
    accent: {
      pink: 'rgba(255, 119, 183, 0.1)',
      blue: 'rgba(123, 196, 255, 0.1)',
      yellow: 'rgba(255, 224, 110, 0.1)',
      purple: 'rgba(167, 139, 250, 0.1)',
    },
  },
};

// Typography Scale
export const typography = {
  // Display styles
  display: {
    large: {
      fontSize: 48,
      lineHeight: 56,
      fontWeight: '700' as const,
      letterSpacing: -0.02,
    },
    medium: {
      fontSize: 36,
      lineHeight: 44,
      fontWeight: '700' as const,
      letterSpacing: -0.02,
    },
  },
  
  // Title styles
  title: {
    large: {
      fontSize: 28,
      lineHeight: 36,
      fontWeight: '600' as const,
      letterSpacing: -0.01,
    },
    medium: {
      fontSize: 24,
      lineHeight: 32,
      fontWeight: '600' as const,
      letterSpacing: -0.01,
    },
    small: {
      fontSize: 20,
      lineHeight: 28,
      fontWeight: '600' as const,
      letterSpacing: -0.01,
    },
  },
  
  // Body styles
  body: {
    large: {
      fontSize: 18,
      lineHeight: 28,
      fontWeight: '400' as const,
      letterSpacing: 0,
    },
    medium: {
      fontSize: 16,
      lineHeight: 24,
      fontWeight: '400' as const,
      letterSpacing: 0,
    },
    small: {
      fontSize: 14,
      lineHeight: 20,
      fontWeight: '400' as const,
      letterSpacing: 0,
    },
  },
  
  // Label and caption styles
  label: {
    fontSize: 14,
    lineHeight: 20,
    fontWeight: '500' as const,
    letterSpacing: 0.01,
  },
  
  caption: {
    fontSize: 12,
    lineHeight: 16,
    fontWeight: '400' as const,
    letterSpacing: 0.01,
  },
};

// Spacing Scale (based on 4px base unit)
export const spacing = {
  xs: BASE_UNIT,      // 4
  sm: BASE_UNIT * 2,  // 8
  md: BASE_UNIT * 3,  // 12
  lg: BASE_UNIT * 4,  // 16
  xl: BASE_UNIT * 5,  // 20
  '2xl': BASE_UNIT * 6,  // 24
  '3xl': BASE_UNIT * 8,  // 32
  '4xl': BASE_UNIT * 10, // 40
  '5xl': BASE_UNIT * 12, // 48
  '6xl': BASE_UNIT * 16, // 64
};

// Border Radius Scale
export const radii = {
  xs: 6,
  sm: 10,
  md: 16,
  lg: 20,
  xl: 28,
  pill: 999,
};

// Elevation/Shadow Presets
export const elevation = {
  x1: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  x2: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 4,
  },
  x3: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 8,
  },
};

// Animation Timing
export const animation = {
  timing: {
    fast: 150,
    normal: 300,
    slow: 500,
    gauge: 1200,
  },
  easing: {
    easeOut: 'ease-out',
    spring: 'spring',
  },
};

// Hit Target Sizes
export const hitTargets = {
  minimum: 44,
  comfortable: 48,
  large: 56,
};

// Progress Ring Configuration
export const progressRing = {
  thickness: 12,
  startAngle: -210,
  endAngle: 30,
  sweepAngle: 240,
  cap: 'round' as const,
};

export default {
  colors,
  typography,
  spacing,
  radii,
  elevation,
  animation,
  hitTargets,
  progressRing,
};
