/**
 * Button Component - shadcn/ui inspired
 * Modern, accessible button with smooth animations
 */

import React from 'react';
import {
  TouchableOpacity,
  Text,
  ViewStyle,
  TextStyle,
  ActivityIndicator,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  runOnJS,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';
import { useTheme } from '../../theme';
import { SPRING_CONFIG, SCALE_VALUES, ANIMATION_PRESETS } from '../../animation/AnimationConfig';
import AnimationManager from '../../animation/AnimationManager';

const AnimatedTouchable = Animated.createAnimatedComponent(TouchableOpacity);

interface ButtonProps {
  children: React.ReactNode;
  onPress?: () => void;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  disabled?: boolean;
  loading?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  hapticFeedback?: boolean;
}

export const Button: React.FC<ButtonProps> = ({
  children,
  onPress,
  variant = 'default',
  size = 'default',
  disabled = false,
  loading = false,
  style,
  textStyle,
  hapticFeedback = true,
}) => {
  const { theme } = useTheme();
  const scale = useSharedValue(1);
  const animationManager = AnimationManager.getInstance();

  const triggerHaptic = () => {
    if (hapticFeedback && !disabled && !loading) {
      try {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      } catch (error) {
        console.log('Haptics not supported:', error);
      }
    }
  };

  const handlePressIn = () => {
    if (disabled || loading) return;

    const animationId = `button-press-${Date.now()}`;
    scale.value = withSpring(SCALE_VALUES.normal, SPRING_CONFIG.snappy);
    
    animationManager.registerAnimation(
      animationId,
      scale,
      'high',
      'spring'
    );

    runOnJS(triggerHaptic)();
  };

  const handlePressOut = () => {
    if (disabled || loading) return;

    const animationId = `button-release-${Date.now()}`;
    scale.value = withSpring(1, SPRING_CONFIG.snappy);
    
    animationManager.registerAnimation(
      animationId,
      scale,
      'high',
      'spring'
    );
  };

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  // Get variant styles
  const getVariantStyles = () => {
    switch (variant) {
      case 'default':
        return {
          backgroundColor: theme.colors.primary,
          borderColor: theme.colors.primary,
          borderWidth: 1,
        };
      case 'destructive':
        return {
          backgroundColor: theme.colors.destructive,
          borderColor: theme.colors.destructive,
          borderWidth: 1,
        };
      case 'outline':
        return {
          backgroundColor: 'transparent',
          borderColor: theme.colors.border,
          borderWidth: 1,
        };
      case 'secondary':
        return {
          backgroundColor: theme.colors.secondary,
          borderColor: theme.colors.secondary,
          borderWidth: 1,
        };
      case 'ghost':
        return {
          backgroundColor: 'transparent',
          borderColor: 'transparent',
          borderWidth: 0,
        };
      case 'link':
        return {
          backgroundColor: 'transparent',
          borderColor: 'transparent',
          borderWidth: 0,
        };
      default:
        return {
          backgroundColor: theme.colors.primary,
          borderColor: theme.colors.primary,
          borderWidth: 1,
        };
    }
  };

  // Get text color based on variant
  const getTextColor = () => {
    switch (variant) {
      case 'default':
        return theme.colors.primaryForeground;
      case 'destructive':
        return theme.colors.destructiveForeground;
      case 'outline':
        return theme.colors.foreground;
      case 'secondary':
        return theme.colors.secondaryForeground;
      case 'ghost':
        return theme.colors.foreground;
      case 'link':
        return theme.colors.primary;
      default:
        return theme.colors.primaryForeground;
    }
  };

  // Get size styles
  const getSizeStyles = () => {
    switch (size) {
      case 'default':
        return {
          height: 40,
          paddingHorizontal: 16,
          paddingVertical: 8,
        };
      case 'sm':
        return {
          height: 36,
          paddingHorizontal: 12,
          paddingVertical: 6,
        };
      case 'lg':
        return {
          height: 44,
          paddingHorizontal: 20,
          paddingVertical: 10,
        };
      case 'icon':
        return {
          height: 40,
          width: 40,
          paddingHorizontal: 0,
          paddingVertical: 0,
        };
      default:
        return {
          height: 40,
          paddingHorizontal: 16,
          paddingVertical: 8,
        };
    }
  };

  const buttonStyle: ViewStyle = {
    ...getVariantStyles(),
    ...getSizeStyles(),
    borderRadius: theme.radii.md,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    opacity: disabled ? 0.5 : 1,
    ...style,
  };

  const textStyles: TextStyle = {
    color: getTextColor(),
    fontSize: size === 'sm' ? 14 : size === 'lg' ? 16 : 15,
    fontWeight: '500',
    ...textStyle,
  };

  return (
    <AnimatedTouchable
      style={[animatedStyle, buttonStyle]}
      onPress={onPress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      disabled={disabled || loading}
      activeOpacity={1}
    >
      {loading && (
        <ActivityIndicator
          size="small"
          color={getTextColor()}
          style={{ marginRight: 8 }}
        />
      )}
      {typeof children === 'string' ? (
        <Text style={textStyles}>{children}</Text>
      ) : (
        children
      )}
    </AnimatedTouchable>
  );
};
