import * as FileSystem from 'expo-file-system';
import { FileItem, FilePermissions, DirectoryContents, FileManagerError } from '../types';

export class FileSystemService {
  private static instance: FileSystemService;

  public static getInstance(): FileSystemService {
    if (!FileSystemService.instance) {
      FileSystemService.instance = new FileSystemService();
    }
    return FileSystemService.instance;
  }

  private constructor() {}

  /**
   * List directory contents
   */
  async listDirectory(path: string): Promise<FileItem[]> {
    try {
      const items = await FileSystem.readDirectoryAsync(path);
      
      const fileItems: FileItem[] = await Promise.all(
        items.map(async (itemName) => {
          const itemPath = `${path}/${itemName}`;
          const info = await FileSystem.getInfoAsync(itemPath);
          const permissions = await this.getFilePermissions(itemPath);
          
          return {
            name: itemName,
            path: itemPath,
            type: info.isDirectory ? 'directory' : 'file',
            size: (info as any).size || 0,
            modifiedDate: (info as any).modificationTime ? new Date((info as any).modificationTime * 1000) : new Date(),
            mimeType: !info.isDirectory ? this.getMimeType(itemName) : undefined,
            isHidden: itemName.startsWith('.'),
            permissions,
          };
        })
      );

      // Sort: directories first, then files, both alphabetically
      return fileItems.sort((a, b) => {
        if (a.type !== b.type) {
          return a.type === 'directory' ? -1 : 1;
        }
        return a.name.toLowerCase().localeCompare(b.name.toLowerCase());
      });
    } catch (error) {
      throw this.handleError(error, 'Failed to list directory');
    }
  }

  /**
   * Create a new directory
   */
  async createDirectory(path: string, name: string): Promise<void> {
    try {
      const fullPath = `${path}/${name}`;
      await FileSystem.makeDirectoryAsync(fullPath, { intermediates: true });
    } catch (error) {
      throw this.handleError(error, 'Failed to create directory');
    }
  }

  /**
   * Delete a file or directory
   */
  async deleteItem(path: string): Promise<void> {
    try {
      const info = await FileSystem.getInfoAsync(path);
      if (!info.exists) {
        throw new Error('File or directory does not exist');
      }
      await FileSystem.deleteAsync(path);
    } catch (error) {
      throw this.handleError(error, 'Failed to delete item');
    }
  }

  /**
   * Copy a file or directory
   */
  async copyItem(source: string, destination: string): Promise<void> {
    try {
      const sourceInfo = await FileSystem.getInfoAsync(source);
      if (!sourceInfo.exists) {
        throw new Error('Source file or directory does not exist');
      }

      if (sourceInfo.isDirectory) {
        await this.copyDirectory(source, destination);
      } else {
        await FileSystem.copyAsync({ from: source, to: destination });
      }
    } catch (error) {
      throw this.handleError(error, 'Failed to copy item');
    }
  }

  /**
   * Move a file or directory
   */
  async moveItem(source: string, destination: string): Promise<void> {
    try {
      const sourceInfo = await FileSystem.getInfoAsync(source);
      if (!sourceInfo.exists) {
        throw new Error('Source file or directory does not exist');
      }

      await FileSystem.moveAsync({ from: source, to: destination });
    } catch (error) {
      throw this.handleError(error, 'Failed to move item');
    }
  }

  /**
   * Rename a file or directory
   */
  async renameItem(path: string, newName: string): Promise<void> {
    try {
      const info = await FileSystem.getInfoAsync(path);
      if (!info.exists) {
        throw new Error('File or directory does not exist');
      }

      const parentDir = path.substring(0, path.lastIndexOf('/'));
      const newPath = `${parentDir}/${newName}`;
      
      await FileSystem.moveAsync({ from: path, to: newPath });
    } catch (error) {
      throw this.handleError(error, 'Failed to rename item');
    }
  }

  /**
   * Get file information
   */
  async getFileInfo(path: string): Promise<FileItem> {
    try {
      const info = await FileSystem.getInfoAsync(path);
      const permissions = await this.getFilePermissions(path);
      const fileName = path.split('/').pop() || '';
      
      return {
        name: fileName,
        path: path,
        type: info.isDirectory ? 'directory' : 'file',
        size: (info as any).size || 0,
        modifiedDate: (info as any).modificationTime ? new Date((info as any).modificationTime * 1000) : new Date(),
        mimeType: !info.isDirectory ? this.getMimeType(fileName) : undefined,
        isHidden: fileName.startsWith('.'),
        permissions,
      };
    } catch (error) {
      throw this.handleError(error, 'Failed to get file info');
    }
  }

  /**
   * Generate thumbnail for image files
   */
  async generateThumbnail(imagePath: string): Promise<string> {
    try {
      const info = await FileSystem.getInfoAsync(imagePath);
      if (!info.exists) {
        throw new Error('Image file does not exist');
      }

      const mimeType = this.getMimeType(imagePath);
      if (!mimeType?.startsWith('image/')) {
        throw new Error('File is not an image');
      }

      // Placeholder: return the original path
      // In a real app, you'd generate and return a thumbnail path
      return imagePath;
    } catch (error) {
      throw this.handleError(error, 'Failed to generate thumbnail');
    }
  }

  /**
   * Get directory contents with metadata
   */
  async getDirectoryContents(path: string): Promise<DirectoryContents> {
    try {
      const items = await this.listDirectory(path);
      
      const totalSize = items.reduce((sum, item) => sum + item.size, 0);
      const itemCount = items.length;

      return {
        path,
        items,
        totalSize,
        itemCount,
      };
    } catch (error) {
      throw this.handleError(error, 'Failed to get directory contents');
    }
  }

  /**
   * Check if path exists
   */
  async exists(path: string): Promise<boolean> {
    try {
      const info = await FileSystem.getInfoAsync(path);
      return info.exists;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get available storage space
   */
  async getStorageInfo(): Promise<{ free: number; total: number }> {
    try {
      const freeSpace = await FileSystem.getFreeDiskStorageAsync();
      const totalSpace = await FileSystem.getTotalDiskCapacityAsync();
      return {
        free: freeSpace,
        total: totalSpace,
      };
    } catch (error) {
      throw this.handleError(error, 'Failed to get storage info');
    }
  }

  // Private helper methods

  private async copyDirectory(source: string, destination: string): Promise<void> {
    await FileSystem.makeDirectoryAsync(destination, { intermediates: true });
    const items = await FileSystem.readDirectoryAsync(source);

    for (const itemName of items) {
      const sourcePath = `${source}/${itemName}`;
      const destPath = `${destination}/${itemName}`;
      const info = await FileSystem.getInfoAsync(sourcePath);

      if (info.isDirectory) {
        await this.copyDirectory(sourcePath, destPath);
      } else {
        await FileSystem.copyAsync({ from: sourcePath, to: destPath });
      }
    }
  }

  private async getFilePermissions(_path: string): Promise<FilePermissions> {
    try {
      // In a real implementation, you'd check actual file permissions
      // For now, we'll return default permissions
      return {
        readable: true,
        writable: true,
        executable: false,
      };
    } catch (error) {
      return {
        readable: false,
        writable: false,
        executable: false,
      };
    }
  }

  private getMimeType(filename: string): string | undefined {
    const extension = filename.toLowerCase().split('.').pop();
    
    const mimeTypes: Record<string, string> = {
      // Images
      jpg: 'image/jpeg',
      jpeg: 'image/jpeg',
      png: 'image/png',
      gif: 'image/gif',
      webp: 'image/webp',
      svg: 'image/svg+xml',
      
      // Documents
      pdf: 'application/pdf',
      doc: 'application/msword',
      docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      xls: 'application/vnd.ms-excel',
      xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      ppt: 'application/vnd.ms-powerpoint',
      pptx: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      
      // Text
      txt: 'text/plain',
      md: 'text/markdown',
      json: 'application/json',
      xml: 'application/xml',
      html: 'text/html',
      css: 'text/css',
      js: 'application/javascript',
      ts: 'application/typescript',
      
      // Audio
      mp3: 'audio/mpeg',
      wav: 'audio/wav',
      ogg: 'audio/ogg',
      m4a: 'audio/mp4',
      
      // Video
      mp4: 'video/mp4',
      avi: 'video/x-msvideo',
      mov: 'video/quicktime',
      wmv: 'video/x-ms-wmv',
      
      // Archives
      zip: 'application/zip',
      rar: 'application/vnd.rar',
      '7z': 'application/x-7z-compressed',
      tar: 'application/x-tar',
      gz: 'application/gzip',
    };

    return extension ? mimeTypes[extension] : undefined;
  }

  private handleError(error: any, message: string): Error {
    console.error(`FileSystemService Error: ${message}`, error);
    
    if (error.code === 'ENOENT') {
      return new Error(`${FileManagerError.FILE_NOT_FOUND}: ${message}`);
    } else if (error.code === 'EACCES') {
      return new Error(`${FileManagerError.PERMISSION_DENIED}: ${message}`);
    } else if (error.code === 'ENOSPC') {
      return new Error(`${FileManagerError.STORAGE_FULL}: ${message}`);
    } else {
      return new Error(`${FileManagerError.INVALID_OPERATION}: ${message}`);
    }
  }
}