import { FileItem } from '../types';

export interface FileTypeInfo {
  category: 'image' | 'document' | 'audio' | 'video' | 'archive' | 'text' | 'code' | 'other';
  icon: string;
  color: string;
  canPreview: boolean;
  canThumbnail: boolean;
}

export class FileTypeService {
  private static instance: FileTypeService;

  public static getInstance(): FileTypeService {
    if (!FileTypeService.instance) {
      FileTypeService.instance = new FileTypeService();
    }
    return FileTypeService.instance;
  }

  private constructor() {}

  /**
   * Get file type information based on file extension
   */
  getFileTypeInfo(filename: string): FileTypeInfo {
    const extension = this.getFileExtension(filename);
    
    const typeMap: Record<string, FileTypeInfo> = {
      // Images
      jpg: { category: 'image', icon: 'image', color: '#10B981', canPreview: true, canThumbnail: true },
      jpeg: { category: 'image', icon: 'image', color: '#10B981', canPreview: true, canThumbnail: true },
      png: { category: 'image', icon: 'image', color: '#10B981', canPreview: true, canThumbnail: true },
      gif: { category: 'image', icon: 'image', color: '#10B981', canPreview: true, canThumbnail: true },
      webp: { category: 'image', icon: 'image', color: '#10B981', canPreview: true, canThumbnail: true },
      svg: { category: 'image', icon: 'image', color: '#10B981', canPreview: true, canThumbnail: false },
      
      // Documents
      pdf: { category: 'document', icon: 'file', color: '#EF4444', canPreview: false, canThumbnail: false },
      doc: { category: 'document', icon: 'file', color: '#3B82F6', canPreview: false, canThumbnail: false },
      docx: { category: 'document', icon: 'file', color: '#3B82F6', canPreview: false, canThumbnail: false },
      xls: { category: 'document', icon: 'file', color: '#10B981', canPreview: false, canThumbnail: false },
      xlsx: { category: 'document', icon: 'file', color: '#10B981', canPreview: false, canThumbnail: false },
      ppt: { category: 'document', icon: 'file', color: '#F59E0B', canPreview: false, canThumbnail: false },
      pptx: { category: 'document', icon: 'file', color: '#F59E0B', canPreview: false, canThumbnail: false },
      
      // Text files
      txt: { category: 'text', icon: 'text', color: '#6B7280', canPreview: true, canThumbnail: false },
      md: { category: 'text', icon: 'text', color: '#6B7280', canPreview: true, canThumbnail: false },
      json: { category: 'code', icon: 'text', color: '#F59E0B', canPreview: true, canThumbnail: false },
      xml: { category: 'code', icon: 'text', color: '#F59E0B', canPreview: true, canThumbnail: false },
      html: { category: 'code', icon: 'text', color: '#EF4444', canPreview: true, canThumbnail: false },
      css: { category: 'code', icon: 'text', color: '#3B82F6', canPreview: true, canThumbnail: false },
      js: { category: 'code', icon: 'text', color: '#F59E0B', canPreview: true, canThumbnail: false },
      ts: { category: 'code', icon: 'text', color: '#3B82F6', canPreview: true, canThumbnail: false },
      
      // Audio
      mp3: { category: 'audio', icon: 'audio', color: '#8B5CF6', canPreview: false, canThumbnail: false },
      wav: { category: 'audio', icon: 'audio', color: '#8B5CF6', canPreview: false, canThumbnail: false },
      ogg: { category: 'audio', icon: 'audio', color: '#8B5CF6', canPreview: false, canThumbnail: false },
      m4a: { category: 'audio', icon: 'audio', color: '#8B5CF6', canPreview: false, canThumbnail: false },
      
      // Video
      mp4: { category: 'video', icon: 'video', color: '#EC4899', canPreview: false, canThumbnail: true },
      avi: { category: 'video', icon: 'video', color: '#EC4899', canPreview: false, canThumbnail: true },
      mov: { category: 'video', icon: 'video', color: '#EC4899', canPreview: false, canThumbnail: true },
      wmv: { category: 'video', icon: 'video', color: '#EC4899', canPreview: false, canThumbnail: true },
      
      // Archives
      zip: { category: 'archive', icon: 'archive', color: '#6B7280', canPreview: false, canThumbnail: false },
      rar: { category: 'archive', icon: 'archive', color: '#6B7280', canPreview: false, canThumbnail: false },
      '7z': { category: 'archive', icon: 'archive', color: '#6B7280', canPreview: false, canThumbnail: false },
      tar: { category: 'archive', icon: 'archive', color: '#6B7280', canPreview: false, canThumbnail: false },
      gz: { category: 'archive', icon: 'archive', color: '#6B7280', canPreview: false, canThumbnail: false },
    };

    return typeMap[extension] || {
      category: 'other',
      icon: 'file',
      color: '#6B7280',
      canPreview: false,
      canThumbnail: false,
    };
  }

  /**
   * Check if file can be previewed
   */
  canPreview(file: FileItem): boolean {
    if (file.type === 'directory') return false;
    const typeInfo = this.getFileTypeInfo(file.name);
    return typeInfo.canPreview;
  }

  /**
   * Check if file can have a thumbnail
   */
  canThumbnail(file: FileItem): boolean {
    if (file.type === 'directory') return false;
    const typeInfo = this.getFileTypeInfo(file.name);
    return typeInfo.canThumbnail;
  }

  /**
   * Get file category
   */
  getFileCategory(filename: string): string {
    const typeInfo = this.getFileTypeInfo(filename);
    return typeInfo.category;
  }

  /**
   * Get file icon name
   */
  getFileIcon(filename: string): string {
    const typeInfo = this.getFileTypeInfo(filename);
    return typeInfo.icon;
  }

  /**
   * Get file color
   */
  getFileColor(filename: string): string {
    const typeInfo = this.getFileTypeInfo(filename);
    return typeInfo.color;
  }

  /**
   * Format file size
   */
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`;
  }

  /**
   * Format date
   */
  formatDate(date: Date): string {
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) {
      return 'Today';
    } else if (diffDays === 2) {
      return 'Yesterday';
    } else if (diffDays <= 7) {
      return `${diffDays - 1} days ago`;
    } else {
      return date.toLocaleDateString();
    }
  }

  /**
   * Get relative time
   */
  getRelativeTime(date: Date): string {
    const now = new Date();
    const diffTime = now.getTime() - date.getTime();
    const diffMinutes = Math.floor(diffTime / (1000 * 60));
    const diffHours = Math.floor(diffTime / (1000 * 60 * 60));
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

    if (diffMinutes < 1) {
      return 'Just now';
    } else if (diffMinutes < 60) {
      return `${diffMinutes}m ago`;
    } else if (diffHours < 24) {
      return `${diffHours}h ago`;
    } else if (diffDays < 7) {
      return `${diffDays}d ago`;
    } else {
      return this.formatDate(date);
    }
  }

  /**
   * Check if file is hidden
   */
  isHidden(filename: string): boolean {
    return filename.startsWith('.');
  }

  /**
   * Get file extension
   */
  private getFileExtension(filename: string): string {
    return filename.toLowerCase().split('.').pop() || '';
  }

  /**
   * Filter files by category
   */
  filterByCategory(files: FileItem[], category: string): FileItem[] {
    return files.filter(file => {
      if (file.type === 'directory') return category === 'folder';
      const typeInfo = this.getFileTypeInfo(file.name);
      return typeInfo.category === category;
    });
  }

  /**
   * Search files by name
   */
  searchFiles(files: FileItem[], query: string): FileItem[] {
    const lowercaseQuery = query.toLowerCase();
    return files.filter(file =>
      file.name.toLowerCase().includes(lowercaseQuery)
    );
  }

  /**
   * Sort files
   */
  sortFiles(files: FileItem[], sortBy: 'name' | 'date' | 'size' | 'type', order: 'asc' | 'desc'): FileItem[] {
    const sorted = [...files].sort((a, b) => {
      // Always put directories first
      if (a.type !== b.type) {
        return a.type === 'directory' ? -1 : 1;
      }

      let comparison = 0;
      
      switch (sortBy) {
        case 'name':
          comparison = a.name.toLowerCase().localeCompare(b.name.toLowerCase());
          break;
        case 'date':
          comparison = a.modifiedDate.getTime() - b.modifiedDate.getTime();
          break;
        case 'size':
          comparison = a.size - b.size;
          break;
        case 'type':
          const aExt = this.getFileExtension(a.name);
          const bExt = this.getFileExtension(b.name);
          comparison = aExt.localeCompare(bExt);
          break;
      }

      return order === 'desc' ? -comparison : comparison;
    });

    return sorted;
  }
}